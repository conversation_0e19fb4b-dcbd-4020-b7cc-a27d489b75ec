import { useRef } from 'react';
import { PermissionsInitial } from 'entities/Permissions';
import { TreeSelect } from 'shared/model';

export interface WorkGroupPassportInnerProps {
  /** Айди кабинета */
  cabinetId: string;
  /** Права ABAC */
  permissions: PermissionsInitial;
  /** Фулсайз дерева */
  isFullSize?: boolean;
}

export type WorkGroupPassportProps = WorkGroupPassportInnerProps & {
  /** Колбек закрытия */
  handleClose: Callback;
  /** Состояние попапа */
  isOpened: boolean;
};

export interface Data {
  /** Дерево */
  treeData: TreeElement[];
}

export interface WorkGroupPassportInitial {
  passport: PassportData;
}

interface PassportData {
  /** Каталоги из шаблонов */
  allCatalogs: ApiData;
  /** Каталоги конкретного кабинета */
  workGroup: ApiData;
}

type ApiData = ApiDefaultKeys & {
  tree: TreeElement[];
};

export interface CheckedTreeElement {
  /** Чекнутые ключи */
  keys: Key[];
  /** Чекнутые ноды дерева */
  nodes: TreeElement[];
}

export interface CurrentCatalogsTreeProps {
  /** Автоколапс родителей */
  autoExpandParent: boolean;
  /** Айди кабинета */
  cabinetId: string;
  /** Может ли редактироваться, считается по правам ABAC */
  canEdit: boolean;
  /** Развернутые ноды */
  expandedKeys: Key[];
  /** Нода, соотвествующая поиску */
  foundKey: Key;
  /** Колбек сохранения днд */
  handleDrag: (body: MoveDNDBody) => Promise<string>;
  /** Колбек разворота нод */
  handleExpand: (keys: Key[]) => void;
  /** Колбек клика по ноде */
  handleSelect: (values: Partial<TreeSelect>) => void;
  /** Полноразмерность */
  isFullSize: boolean;
  /** Перезапрос дерева */
  refetchTree: Callback;
  /** Сброс выбора ноды */
  resetSelect: Callback;
  /** Установка состояния днд */
  setIsDrag: (value: boolean) => void;
  /** Реф дерева */
  treeRef: typeof useRef;
  /** Значения селекта */
  treeValues: TreeSelect;
}
