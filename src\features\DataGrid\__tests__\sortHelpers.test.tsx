// utils.test.ts
import {
  compareStrings,
  isNumber,
  compareNumbers,
  isEmptyOrNull,
} from '../lib/sortHelpers';

describe('compareStrings', () => {
  it('должен возвращать 0, если строки равны', () => {
    expect(compareStrings('abc', 'abc')).toBe(0);
  });

  it('должен корректно сравнивать строки, если обе строки не равны null и не пусты', () => {
    expect(compareStrings('abc', 'abd')).toBeLessThan(0);
    expect(compareStrings('abd', 'abc')).toBeGreaterThan(0);
  });

  it('должен корректно сравнивать строки с разным регистром и числами в строках', () => {
    expect(compareStrings('a', 'A')).toBe(0);
    expect(compareStrings('file1', 'file2')).toBeLessThan(0);
    expect(compareStrings('1file2', '2file1')).toBeLessThan(0);
    expect(compareStrings('17000 aaaa', '17000 bbbb')).toBeLessThan(0);
    expect(compareStrings('17000 zzzz', '17000 aaaa')).toBeGreaterThan(0);
  });
});

describe('isNumber', () => {
  it('должен возвращать true для строк с целыми числами', () => {
    expect(isNumber('123')).toBe(true);
  });

  it('должен возвращать true для строк с десятичными числами', () => {
    expect(isNumber('123.456')).toBe(true);
  });

  it('должен возвращать true для отрицательных чисел', () => {
    expect(isNumber('-123')).toBe(true);
  });

  it('должен возвращать false для строк, не являющихся числами', () => {
    expect(isNumber('abc')).toBe(false);
  });

  it('должен возвращать false для строки NaN', () => {
    expect(isNumber('NaN')).toBe(false);
  });

  it('должен возвращать true для строк с начальными и конечными пробелами', () => {
    expect(isNumber(' 123 ')).toBe(true);
  });
});

describe('compareNumbers', () => {
  it('должен возвращать 0, если числа равны', () => {
    expect(compareNumbers('123', '123')).toBe(0);
  });

  it('должен возвращать отрицательное число, если первое меньше второго', () => {
    expect(compareNumbers('123', '124')).toBeLessThan(0);
  });

  it('должен возвращать положительное число, если первое больше второго', () => {
    expect(compareNumbers('125', '124')).toBeGreaterThan(0);
  });

  it('должен корректно сравнивать отрицательные числа', () => {
    expect(compareNumbers('-5', '-3')).toBeLessThan(0);
    expect(compareNumbers('-3', '-5')).toBeGreaterThan(0);
  });

  it('должен обрабатывать числа с начальными и конечными пробелами', () => {
    expect(compareNumbers(' 123 ', '123')).toBe(0);
  });

  it('должен сравнивать дробные числа', () => {
    expect(compareNumbers('123.9', '123.1')).toBeGreaterThan(0);
  });
});

describe('isEmptyOrNull', () => {
  it('должен возвращать true для null', () => {
    expect(isEmptyOrNull(null)).toBe(true);
  });

  it('должен возвращать true для undefined', () => {
    expect(isEmptyOrNull(undefined)).toBe(true);
  });

  it('должен возвращать true для пустой строки', () => {
    expect(isEmptyOrNull('')).toBe(true);
  });

  it('должен возвращать true для строки, содержащей только пробелы', () => {
    expect(isEmptyOrNull('   ')).toBe(true);
  });

  it('должен возвращать true для строки "null"', () => {
    expect(isEmptyOrNull('null')).toBe(true);
  });

  it('должен возвращать false для непустой строки', () => {
    expect(isEmptyOrNull('abc')).toBe(false);
  });

  it('должен возвращать false для числа ноль', () => {
    expect(isEmptyOrNull(0)).toBe(false);
  });

  it('должен возвращать false для булевого значения false', () => {
    expect(isEmptyOrNull(false)).toBe(false);
  });
});
