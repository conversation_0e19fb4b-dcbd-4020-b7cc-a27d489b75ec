import { createSlice } from '@reduxjs/toolkit';

import { getAuthenticatedThunk } from './actions';
import { UserInfoInitialState } from './types';

export const initialState = {
  error: null,
  isPending: false,
  auth: null,
} as UserInfoInitialState;

export const slice = createSlice({
  name: 'userInfo',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
  },
  extraReducers: (builder) => {
    builder.addCase(getAuthenticatedThunk.pending, (state) => {
      state.error = null;
      state.isPending = true;
    });

    builder.addCase(getAuthenticatedThunk.fulfilled, (state, { payload }) => {
      state.isPending = false;
      state.auth = payload;
    });

    builder.addCase(getAuthenticatedThunk.rejected, (state, { error }) => {
      state.error = error;
      state.isPending = false;
    });
  },
});
