import { TreeProps } from 'antd';
import { useCallback, useState } from 'react';
import { windowIdLib } from 'entities/WindowId';
import { apiUrls } from 'shared/api';
import {
  appErrorNotification,
  generateUrlWithQueryParams,
  setChildrenToLazyTree,
  treeParserByConfig,
} from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

type LoadData = NonNullable<TreeProps<TreeElement>['loadData']>;

export const useTreeData = (
  startedTreeData: TreeElement[],
  tableEndpoint: string,
): [TreeElement[], LoadData] => {
  const [trigger] = useAxiosRequest<{ treeData: TreeElement[] }>();
  const windowId = windowIdLib.useWindowId();
  const parserConfig = (node: TreeElement): TreeElement => ({
    ...node,
    isDirectory: !node.isLeaf,
    color:
      'fileNetId' in node
        ? node.fileNetId !== '' && Boolean(node.canView)
          ? '#1890ff'
          : '#ff4d4f'
        : 'inherit',
  });

  const treeDataInitial = treeParserByConfig(startedTreeData, parserConfig);

  const [treeData, setTreeData] = useState<TreeElement[]>(treeDataInitial);

  const onLoadData = useCallback<LoadData>(
    async ({ itemId, pos, isCallable }) =>
      new Promise<void>((resolve, reject) => {
        if (!isCallable) {
          /** Если дети не вызываемые выходит из функции */
          resolve();
        } else {
          trigger(
            generateUrlWithQueryParams(
              apiUrls.defaultPages.tree(tableEndpoint),
              {
                itemId,
                windowId,
                pos,
              },
            ),
          )
            .then(({ treeData: treeLeafs }) => {
              setTreeData((prev) =>
                setChildrenToLazyTree(
                  prev,
                  pos,
                  treeParserByConfig(treeLeafs, parserConfig),
                ),
              );
              resolve();
            })
            .catch((err) => {
              appErrorNotification(
                `Произошла ошибка при загрузке листьев дерева по ключу ${pos}. Хук useTreeData`,
                err,
              );
              reject();
            });
        }
      }),
    [windowId, tableEndpoint], // eslint-disable-line
  );

  return [treeData, onLoadData];
};
