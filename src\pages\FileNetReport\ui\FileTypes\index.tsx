import { DeleteOutlined, FolderOutlined } from '@ant-design/icons';
import { notification, Table } from 'antd';
import { FC, useState } from 'react';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { createTableModal } from 'features/TableModal';
import { indexFilesStore } from 'entities/IndexFilesTable';
import {
  createConfirmModal,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { ApiContainer } from 'shared/ui';

import { reducers, selectors } from '../../store';

export const FileTypes: FC = () => {
  const [selected, setSelected] = useState<{
    keys: Key[];
    rows: TableRowData[];
  }>({ rows: [], keys: [] });

  const { deleteSelectedFileTypes } = useCreateSliceActions(
    reducers.slice.actions,
  );
  const { selectedKeys, selectedRows } = useAppSelector(
    selectors.fileTypesSelector,
  );
  const { addSelectedFileTypes } = useCreateSliceActions(
    reducers.slice.actions,
  );

  const [
    {
      isPending,
      error,
      table: { rows, columns },
    },
    refresh,
  ] = indexFilesStore.hooks.useGetIndexFiles();

  const allRowsSelected = selectedRows.length === rows.length;

  return (
    <ApiContainer
      error={error}
      isPending={isPending}
      errorStatus="warning"
      errorTitle="Ошибка загрузки файловой структуры"
      refresh={refresh}
    >
      <DataGrid
        columns={columns}
        rows={selectedRows}
        tableAdditionProps={{
          size: 'small',
          scroll: { x: '100%', y: 340 },
          bordered: true,
          rowSelection: {
            columnWidth: 80,
            selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
            selectedRowKeys: selected.keys,
            onChange: (selectedRowKeys, changedRows) => {
              setSelected({ rows: changedRows, keys: selectedRowKeys });
            },
          },
        }}
        additionalButtons={[
          {
            icon: <FolderOutlined />,
            type: 'primary',
            title: 'Выбрать',
            key: 'select-sabah',
            ...(allRowsSelected && {
              tooltip: 'Все элементы добавлены - нет элементов для выбора',
            }),
            disabled: allRowsSelected,
            onClick: () =>
              createTableModal('Типы файлов', {
                onSave: async (savedRows) => {
                  addSelectedFileTypes(savedRows);
                  notification.success({
                    message: 'Выбранные типы файлов успешно добавлены',
                  });
                  return true;
                },
                hideKeys: selectedKeys,
                tableData: { rows, columns },
              }),
          },
          {
            icon: <DeleteOutlined />,
            type: 'primary',
            danger: true,
            ghost: true,
            title: 'Удалить выбранное',
            disabled: selected.rows.length === 0,
            tooltip: 'Необходимо выбрать элементы',
            key: 'delete-sabah',
            onClick: createConfirmModal({
              isCallback: true,
              title: 'Удаление',
              message: 'Вы дейстивтельно хотите удалить выбранные элементы?',
              isConfirmDanger: true,
              buttonNames: {
                confirm: 'Удалить',
              },
              onConfirm: () => {
                deleteSelectedFileTypes(selected.rows);
                setSelected({ rows: [], keys: [] });
              },
            }),
          },
        ]}
      />
    </ApiContainer>
  );
};
