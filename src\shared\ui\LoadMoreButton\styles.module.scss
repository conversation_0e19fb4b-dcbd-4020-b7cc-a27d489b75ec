.container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;

  &.default {
    margin: 8px 0;
  }

  &.compact {
    margin: 4px 0;
  }

  &.minimal {
    margin: 2px 0;
    padding: 4px 0;
  }
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border-style: dashed;
  border-color: #d9d9d9;
  background: transparent;
  transition: all 0.2s ease;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
    background: rgba(64, 169, 255, 0.04);
  }

  &:focus {
    border-color: #40a9ff;
    color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
  }

  &.loading {
    cursor: progress;
    opacity: 0.7;

    &:hover {
      border-color: #d9d9d9;
      color: inherit;
      background: transparent;
    }
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      border-color: #d9d9d9;
      color: inherit;
      background: transparent;
    }
  }
}

.icon {
  font-size: 12px;
  transition: transform 0.2s ease;

  .button:hover & {
    transform: translateY(1px);
  }

  .loading & {
    animation: spin 1s linear infinite;
  }
}

.text {
  font-size: 12px;
  font-weight: 400;
  white-space: nowrap;
}

// Варианты размеров
.container.default .button {
  min-width: 120px;
  height: 28px;
  padding: 4px 12px;
  font-size: 13px;
}

.container.compact .button {
  min-width: 80px;
  height: 24px;
  padding: 2px 8px;
  font-size: 12px;
}

.container.minimal .button {
  min-width: 40px;
  height: 20px;
  padding: 1px 6px;
  font-size: 11px;
  border: 1px solid #f0f0f0;

  &:hover {
    border-color: #d9d9d9;
  }
}

// Анимация загрузки
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
