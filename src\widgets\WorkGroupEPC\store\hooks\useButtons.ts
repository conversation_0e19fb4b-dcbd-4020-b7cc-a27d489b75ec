import { notification } from 'antd';
import { useMemo } from 'react';
import {
  EpcPermissions,
  TogglePopup,
  WorkGroupEPCStore,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import { permissionsConfig } from 'entities/Permissions';
import { apiUrls } from 'shared/api';
import { getParentsNodes } from 'shared/lib/getParentNodes';
import {
  createConfirmModal,
  useAppDispatch,
  useAppSelector,
} from 'shared/model';

export const useButtons = (
  selectedNodes: TreeElement[],
  selectedFiles: TreeElement[],
  selectedDirs: TreeElement[],
  refetchTree: Callback,
  togglePopup: TogglePopup,
  epcPermissions: EpcPermissions,
  handleExpand: (key: Key[]) => void,
  refetchNode: ({ itemId, isDirectory, key }: TreeElement) => Promise<void>,
): AdditionalButton[] => {
  const dispatch = useAppDispatch();
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  const parsedFiles = useMemo(
    () =>
      selectedFiles.reduce(
        (acc, item) => {
          if (item.isRemovable) {
            acc.deletable.push(item.itemId as Key);
          }

          if (item.permissions?.canDownload) {
            acc.downloadable.push(item.itemId as Key);
          }

          return acc;
        },
        {
          deletable: [],
          downloadable: [],
        } as { deletable: Key[]; downloadable: Key[] },
      ),
    [selectedFiles],
  );

  const selectedNodesWithoutRoot = useMemo(
    () =>
      selectedNodes.filter((node) => node.key !== WorkGroupEPCConfig.ROOT_KEY),
    [selectedNodes],
  );

  const selectedDirsWithoutRoot = useMemo(
    () => selectedDirs.filter((dir) => dir.key !== WorkGroupEPCConfig.ROOT_KEY),
    [selectedDirs],
  );

  const refetchParentNodes = async (): Promise<void> => {
    const parents = getParentsNodes(epc.treeData.tree, parsedFiles.deletable);
    parents.forEach((node) => {
      refetchNode(node);
    });
  };

  return [
    {
      title: 'Скачать файлы с описью',
      key: 'download',
      type: 'primary',
      loading: epc.buttons.isPending,
      disabled:
        !epcPermissions.canDownloadFiles ||
        (parsedFiles.downloadable.length === 0 && selectedDirs?.length === 0),
      tooltip: !epcPermissions.canDownloadFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
        : parsedFiles.downloadable.length === 0
        ? 'Не выбраны файлы для скачивания'
        : undefined,
      onClick: () => togglePopup('downloadEPC'),
    },
    {
      title: 'Удалить файлы',
      key: 'delete',
      danger: true,
      ghost: true,
      disabled:
        !epcPermissions.canDeleteFiles || parsedFiles.deletable.length === 0,
      tooltip: !epcPermissions.canDeleteFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('удаление')
        : parsedFiles.deletable.length === 0
        ? 'Не выбраны файлы для удаления или нет доступных файлов для удаления'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        createConfirmModal({
          title: 'Внимание',
          message: 'Вы действительно хотите удалить файлы?',
          onConfirm: async () => {
            await dispatch(
              WorkGroupEPCStore.actions.postDeleteFiles({
                url: apiUrls.workGroup.fileData.deleteFile,
                body: parsedFiles.deletable,
              }),
            )
              .unwrap()
              .then(() => {
                notification.success({ message: 'Файлы успешно удалены' });
                refetchParentNodes();
              });
          },
        });
      },
    },
    {
      title: 'Доступность элементов в КВ',
      key: 'viewEnable',
      type: 'default',
      disabled:
        !epcPermissions.canEdit || selectedNodesWithoutRoot.length === 0,
      tooltip: !epcPermissions.canEdit
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение доступности',
          )
        : selectedNodesWithoutRoot.length === 0
        ? 'Не выбраны элементы для изменения доступности'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        togglePopup('visibility');
      },
    },
    {
      title: 'Копировать',
      key: 'copyFiles',
      type: 'default',
      disabled:
        !epcPermissions.canCopyFiles ||
        (selectedFiles.length === 0 && selectedDirsWithoutRoot.length === 0),
      tooltip: !epcPermissions.canCopyFiles
        ? permissionsConfig.warnMessages.noPermissionsDefault('копированиe')
        : selectedFiles.length === 0 && selectedDirsWithoutRoot.length === 0
        ? 'Не выбраны элементы, доступные для копирования'
        : undefined,
      loading: epc.buttons.isPending,
      onClick: () => {
        togglePopup('copy');
      },
    },
    {
      title: 'Обновить',
      key: 'refetch',
      type: 'default',
      loading: epc.buttons.isPending,
      onClick: () => {
        handleExpand([]);
        refetchTree();
      },
    },
  ];
};
