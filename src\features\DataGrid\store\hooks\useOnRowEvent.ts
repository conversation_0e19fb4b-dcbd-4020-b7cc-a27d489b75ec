import { TableProps } from 'antd';
import { MouseEvent, useCallback } from 'react';
import { DataGridProps, TableRowData } from '../..';

const validateMouseEventTarget = (event: MouseEvent): boolean =>
  /** Проверка на то что элемент является иконкой или кнопкой */
  ['button', 'svg', 'path'].includes((event.target as Element).localName) ||
  /** Проверка на то что элемент является тултипом */
  (event.target as HTMLDivElement)?.className?.includes('tooltip-inner');

export const useOnRowEvent = (
  tableAdditionHandlers: DataGridProps['tableAdditionHandlers'],
): TableProps<TableRowData>['onRow'] =>
  useCallback(
    (record) => {
      let singleClickTimer: ReturnType<typeof setTimeout>;
      let firstClickEvent = false;

      return {
        onClick: (event: MouseEvent) => {
          if (!firstClickEvent) {
            firstClickEvent = true;
            singleClickTimer = setTimeout(() => {
              if (validateMouseEventTarget(event)) {
                return;
              }

              if (tableAdditionHandlers) {
                tableAdditionHandlers.onRowClick(record, false);
              }
              firstClickEvent = false;
            }, 300);
          }
        },
        onDoubleClick: (event: MouseEvent) => {
          clearTimeout(singleClickTimer);
          if (validateMouseEventTarget(event)) {
            return;
          }

          if (tableAdditionHandlers) {
            tableAdditionHandlers.onRowClick(record, true);
          }
          firstClickEvent = false;
        },
      };
    },
    [tableAdditionHandlers],
  );
