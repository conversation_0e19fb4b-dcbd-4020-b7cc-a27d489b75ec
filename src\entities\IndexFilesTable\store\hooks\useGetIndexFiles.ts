import { useCallback, useEffect } from 'react';
import { IndexFilesInitial, indexFilesStore } from 'entities/IndexFilesTable';
import { useAppDispatch, useAppSelector } from 'shared/model';

export const useGetIndexFiles = (): [IndexFilesInitial, Callback] => {
  const dispatch = useAppDispatch();
  const indexFiles = useAppSelector(
    indexFilesStore.selectors.indexTableSelector,
  );

  const getTable = useCallback(() => {
    dispatch(indexFilesStore.thunks.getIndexFilesThunk());
  }, [dispatch]);

  useEffect(() => {
    if (indexFiles.table.rows.length === 0) {
      getTable();
    }
  }, [indexFiles.table.rows, getTable]);

  return [indexFiles, getTable];
};
