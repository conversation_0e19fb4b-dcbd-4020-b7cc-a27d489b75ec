.drawer {
  :global(.ant-drawer-header) {
    border-bottom: none !important;
    padding: 24px 16px;
  }

  :global(.ant-drawer-close) {
    margin-left: auto;
  }

  :global(.ant-drawer-body) {
    padding: 0;
  }

  :global(.ant-drawer-footer) {
    border-top: none !important;
    padding: 0 16px 10px !important;
  }
}

.table {
  height: 510px;
}

.cellRow {
  display: flex;
  gap: 5px;
  align-items: center;
}

.cellTextTed {
  color: #ff7d7d;
}
