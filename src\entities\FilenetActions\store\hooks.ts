import { notification } from 'antd';
import { useCallback } from 'react';
import { useCopyToClipboard } from 'react-use';
import { apiUrls, filenetServiceInstance, getForBlobFile } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useGetViewer } from 'shared/model/hooks';
import { UseActions } from '..';

export const useActions: UseActions = (cardId, attachment) => {
  const [, copyToClipboard] = useCopyToClipboard();
  const getViewer = useGetViewer();

  const onCopy = useCallback(() => {
    copyToClipboard(
      generateUrlWithQueryParams(
        window.location.origin + window.location.pathname,
        { tableType: 'cards', popupId: cardId },
      ),
    );
    notification.success({
      message: 'Буфер обмена',
      description: 'Ссылка скопирована!',
    });
  }, [cardId]); // eslint-disable-line

  const onView = useCallback(async () => {
    try {
      getViewer(attachment.id, attachment.filename, cardId);
    } catch (err) {
      appErrorNotification(
        'Ошибка получения ссылки на просмотр файла',
        err as AppError,
      );
    }
  }, [attachment.filename, attachment.id]); //eslint-disable-line

  const onDownload = useCallback(async () => {
    try {
      await filenetServiceInstance.get(
        apiUrls.fileNet.sendToGluster(attachment.id),
      );

      await getForBlobFile(
        apiUrls.fileNet.downloadFile(attachment.id),
        attachment.filename,
        filenetServiceInstance,
      );
    } catch (err) {
      appErrorNotification(
        'Ошибка получения ссылки на загрузку файла',
        err as AppError,
      );
    }
  }, [attachment.filename, attachment.id]);

  return {
    onCopy,
    onView,
    onDownload,
  };
};
