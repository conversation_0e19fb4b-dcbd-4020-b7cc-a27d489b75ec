import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TableRowData } from 'features/DataGrid';

import { WorkGroupCreationPlanInitial } from '..';
import { getDataThunk, updateDataThunk } from './actions';

export const initialState: WorkGroupCreationPlanInitial = {
  plan: {
    table: { isPending: false, error: null, rows: [], columns: [] },
    schedulerValue: null,
    buttons: { isPending: false, error: null },
    searchInputs: {},
  },
};

export const slice = createSlice({
  name: 'workGroupCreationPlan',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
    handleResetSearch: (state) => {
      state.plan.searchInputs = { ...initialState.plan.searchInputs };
      state.plan.table = {
        ...initialState.plan.table,
        columns: state.plan.table.columns,
      };
      state.plan.schedulerValue = null;
    },
    handleChecked: (state, { payload }: PayloadAction<number>) => {
      const newRow = JSON.parse(JSON.stringify(state.plan.table.rows[payload]));
      newRow.rowId.checked = !state.plan.table.rows[payload].rowId?.checked;
      state.plan.table.rows[payload] = newRow;
    },
    handleRowCheck: (state, { payload }: PayloadAction<number>) => {
      const newRow = JSON.parse(JSON.stringify(state.plan.table.rows[payload]));
      newRow.rowId.checkedRows =
        !state.plan.table.rows[payload].rowId?.checkedRows;
      state.plan.table.rows[payload] = newRow;
    },
    handleChangeScheduler: (state, { payload }: PayloadAction<string>) => {
      state.plan.schedulerValue = payload;

      // Если дата пустая, не обновляем даты в строках
      if (payload.trim() === '') {
        return;
      }

      const newRow = JSON.parse(JSON.stringify(state.plan.table.rows));

      state.plan.table.rows = newRow.map((item: TableRowData) => ({
        ...item,
        rowId: {
          ...item.rowId,
          date: item.rowId?.checkedRows ? payload : item.rowId?.date,
        },
      }));
    },
    handleChangeDate: (
      state,
      { payload }: PayloadAction<{ key: number; value: string }>,
    ) => {
      const newRow = JSON.parse(
        JSON.stringify(state.plan.table.rows[payload.key]),
      );
      newRow.rowId.date = payload.value;
      state.plan.table.rows[payload.key] = newRow;
    },
    handleSwitch: (
      state,
      { payload }: PayloadAction<{ checked: boolean; key: number }>,
    ) => {
      const newRow = JSON.parse(
        JSON.stringify(state.plan.table.rows[payload.key]),
      );
      newRow.rowId.isAutomatic = payload.checked;
      newRow.COLUMN_12 = newRow.rowId.isAutomatic
        ? 'Автоматический режим'
        : 'Ручной режим';
      state.plan.table.rows[payload.key] = newRow;
    },
    handleSearchingInput: (
      state,
      { payload }: PayloadAction<Record<string, unknown>>,
    ) => {
      state.plan.searchInputs = {
        ...state.plan.searchInputs,
        ...payload,
      };
    },
    handleRowInputs: (
      state,
      {
        payload,
      }: PayloadAction<{ column: string; key: number; value: string }>,
    ) => {
      const newRow = JSON.parse(
        JSON.stringify(state.plan.table.rows[payload.key]),
      );

      if (payload.column === 'COLUMN_9') {
        newRow.rowId.textDescription = payload.value;
        newRow[payload.column] = payload.value;
      }

      if (payload.column === 'COLUMN_10') {
        newRow.rowId.reason = payload.value;
        newRow[payload.column] = payload.value;
      }

      if (payload.column === 'COLUMN_11') {
        newRow.rowId.comment = payload.value;
        newRow[payload.column] = payload.value;
      }

      state.plan.table.rows[payload.key] = newRow;
    },
    handleCheckAllRows: (state, { payload }: PayloadAction<boolean>) => {
      const newRow = JSON.parse(JSON.stringify(state.plan.table.rows));

      state.plan.table.rows = newRow.map((row: TableRowData) => ({
        ...row,
        rowId: { ...row.rowId, checkedRows: payload },
      }));
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getDataThunk.pending, (state) => {
      state.plan.table.isPending = true;
      state.plan.table.error = null;
    });

    builder.addCase(getDataThunk.rejected, (state, { error }) => {
      state.plan.table.error = error;
      state.plan.table.isPending = false;
    });

    builder.addCase(getDataThunk.fulfilled, (state, { payload }) => {
      state.plan.schedulerValue = null;
      state.plan.table.isPending = false;
      state.plan.table.columns = payload.columns;
      state.plan.table.rows = payload.rows;
      state.plan.table.rows = payload.rows.map((row) => {
        row.COLUMN_8 = row?.rowId?.date;
        return row;
      });
    });

    builder.addCase(updateDataThunk.pending, (state) => {
      state.plan.buttons.isPending = true;
      state.plan.buttons.error = null;
    });

    builder.addCase(updateDataThunk.rejected, (state, { error }) => {
      state.plan.buttons.isPending = false;
      state.plan.buttons.error = error;
    });

    builder.addCase(updateDataThunk.fulfilled, (state) => {
      state.plan.buttons.isPending = false;
    });
  },
});

export default slice.reducer;
