import { EventDataNode } from 'antd/lib/tree';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  UseTreeTypes,
  WorkGroupEPCConfig,
  WorkGroupEPCLib,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { apiUrls, baseAppInstance } from 'shared/api';
import { useExpandTreeActions, getFilteredFlatTreeByParam } from 'shared/lib';
import {
  useAppDispatch,
  useAppSelector,
  useAxiosRequest,
  useCreateSliceActions,
  useLazyTreeData,
  useLazyTreeDataWithPagination,
  useTreeSearchArrow,
  useTreeSelection,
} from 'shared/model';
import { slice } from '../reducer';

export const useEPCTree = (
  cabinetId: string,
  togglePopup: (
    name: keyof typeof WorkGroupEPCConfig.popupsInitial,
    status?: boolean | undefined,
  ) => void,
): UseTreeTypes => {
  const dispatch = useAppDispatch();
  const epcData = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  const [treeValues, handleSelect, handleReset] = useTreeSelection();
  const [isDrag, setIsDrag] = useState(true);

  const [handleMove, getEPCTree] =
    WorkGroupEPCStore.hooks.useEPCActions(cabinetId);

  const setLazyTree = (tree: TreeElement[]): void => {
    dispatch(slice.actions.handleTree(tree));
  };

  const [autoExpandParent, expandedKeys, handleExpand, handleAutoExpand] =
    useExpandTreeActions(epcData.treeData.tree, 'tagged', isDrag, true);

  const { loadedKeys, loadedKeysSet, setLoadedKeys, onLoadData, refetchNode } =
    useLazyTreeData(
      epcData.treeData.tree,
      apiUrls.workGroup.EPC.getLazyTree,
      cabinetId,
      setLazyTree,
      WorkGroupEPCConfig.IS_CABINET,
      handleExpand,
      WorkGroupEPCConfig.updateMainFilePermissions,
    );

  const [checked, handleCheck, resetCheck, setChecked] =
    WorkGroupEPCStore.hooks.useTreeCheck(loadedKeysSet);

  // Ставим чекбоксы загруженным детям, если родитель выделен
  const onLoadDataAndCheck = useCallback(
    async (treeNode: EventDataNode<TreeElement>) => {
      try {
        const children = await onLoadData(treeNode);

        if (!children || !Array.isArray(children)) {
          return Promise.resolve();
        }

        if (!checked.keys.includes(treeNode.key)) {
          return Promise.resolve();
        }

        const { selectedFiles, selectedDirs } = children.reduce(
          (result, node) => {
            if (node?.isDirectory && !loadedKeysSet.has(node.key)) {
              result.selectedDirs.push(node);
            } else if (!node?.isDirectory) {
              result.selectedFiles.push(node);
            }
            return result;
          },
          { selectedFiles: [], selectedDirs: [] } as {
            selectedDirs: TreeElement[];
            selectedFiles: TreeElement[];
          },
        );

        setChecked((values) => ({
          keys: values.keys.concat(children.map((node) => node.key)),
          nodes: values.nodes.concat(children),
          selectedDirs: values.selectedDirs.concat(selectedDirs),
          selectedFiles: values.selectedFiles.concat(selectedFiles),
        }));

        return Promise.resolve();
      } catch (err) {
        handleExpand(loadedKeys);
        return Promise.resolve();
      }
    },
    [
      checked.keys,
      handleExpand,
      loadedKeys,
      loadedKeysSet,
      onLoadData,
      setChecked,
    ],
  );

  // Обеспечиваем последовательное завершение событий loadData APPID-2052
  const loadQueue = useRef<Promise<void>[]>([]);
  const loadTreeNodeData = async (
    node: EventDataNode<TreeElement>,
  ): Promise<void> => {
    const currentIndex = loadQueue.current.length;
    const loadPromise = onLoadDataAndCheck(node);

    // Добавляем новую задачу в очередь
    loadQueue.current.push(loadPromise);

    // Ждем завершения всех предыдущих промисов до текущего индекса
    if (currentIndex > 0) {
      const currentQueue = loadQueue.current.slice(0, currentIndex);
      await Promise.all(currentQueue);
    }

    // Дожидаемся выполнения текущей задачи
    const result = await loadPromise;

    // Удаляем из очереди отработавшие промисы
    loadQueue.current.splice(0, currentIndex + 1);

    return result;
  };

  const [isReqEnd, setIsReqEnd, handleSearch] =
    WorkGroupEPCStore.hooks.useTreeSearch(
      cabinetId,
      togglePopup,
      treeValues,
      loadedKeys,
      expandedKeys,
      setLoadedKeys,
      handleExpand,
    );

  const refetchTree = useCallback(() => {
    getEPCTree();
    setLoadedKeys([]);
    handleExpand([]);
    handleReset();
    resetCheck();
  }, [getEPCTree, handleExpand, handleReset, resetCheck, setLoadedKeys]);

  const [treeRef, tagged, currentIndex, foundKey, arrowUp, arrowDown] =
    useTreeSearchArrow(epcData.treeData.tree, '', handleAutoExpand);

  const flatTree = useMemo(
    () => getFilteredFlatTreeByParam(epcData.treeData.tree, 'key'),
    [epcData.treeData.tree],
  );

  const { useTreeLink } = WorkGroupEPCLib;
  const { handleUpdateLinked, handleTree } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );
  const [
    isLinkedReqEnd,
    setIsLinkedReqEnd,
    foundLink,
    resetFoundLinkKey,
    handleLinkClick,
  ] = useTreeLink(
    epcData.treeData.tree,
    treeRef,
    expandedKeys,
    handleExpand,
    handleAutoExpand,
    loadedKeys,
    setLoadedKeys,
    cabinetId,
  );

  const [getLinked] = useAxiosRequest<TreeElement[]>(baseAppInstance);

  const refetchNodeWithReset = useCallback(
    async (node: TreeElement): Promise<void> => {
      resetCheck();
      await refetchNode(node);
    },
    [refetchNode, resetCheck],
  );

  const handleResetTree = useCallback(() => {
    refetchTree();
    setIsReqEnd(false);
    resetFoundLinkKey();
  }, [refetchTree, resetFoundLinkKey, setIsReqEnd]);

  const arrowUpHandler = useCallback(() => {
    setIsDrag(false);
    arrowUp();
  }, [arrowUp]);

  const arrowDownHandler = useCallback(() => {
    setIsDrag(false);
    arrowDown();
  }, [arrowDown]);

  return {
    tree: {
      error: epcData.treeData.error,
      isPending: epcData.treeData.isPending,
      data: epcData.treeData.tree,
      linkedData: epcData.linkedData,
      handleTree,
      handleResetTree,
    },
    treeSelect: {
      selectedValues: treeValues,
      handleSelect,
      handleReset,
    },
    treeDrag: {
      isDrag,
      setIsDrag,
    },
    treeSearch: {
      isReqEnd,
      setIsReqEnd,
      handleSearch,
    },
    treeActions: {
      handleMove,
      refetchTree,
      refetchNode: refetchNodeWithReset,
    },
    treeCheck: {
      checked,
      handleCheck,
      resetCheck,
    },
    treeLoad: {
      loadedKeys,
      loadedKeysSet,
      onLoadData: loadTreeNodeData,
      setLoadedKeys,
    },
    treeExpand: {
      autoExpandParent,
      expandedKeys,
      handleExpand,
    },
    treeRefs: {
      treeRef,
    },
    treeArrow: {
      tagged,
      currentIndex,
      foundKey,
      arrowUp: arrowUpHandler,
      arrowDown: arrowDownHandler,
    },
    treeLink: {
      isLinkedReqEnd,
      setIsLinkedReqEnd,
      resetFoundLinkKey,
      foundLink,
      handleLinkClick,
      handleUpdateLinked,
      getLinked,
    },
    flatTree,
  };
};
