# EPC Tree Pagination - Упрощенная реализация

## Обзор

Минимально работающая реализация кнопки "Загрузить еще" для EPC дерева с возможностью постепенной загрузки дочерних элементов узлов, содержащих много файлов.

## Основные компоненты

### 1. Константы (`src/widgets/WorkGroupEPC/config/constants.ts`)
```typescript
export const PAGINATION_THRESHOLD = 15;
```
- Единственная константа, определяющая порог для включения пагинации

### 2. Типы (`src/widgets/WorkGroupEPC/types.ts`)
```typescript
// Информация о состоянии пагинации для узла дерева
export interface NodePaginationInfo {
  currentPage: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  loadedItemsCount: number;
}

// Состояние пагинации для всех узлов дерева
export type TreePaginationState = Map<string, NodePaginationInfo>;

// Колбэк для загрузки дополнительных элементов
export type LoadMoreCallback = (nodeKey: string) => Promise<void>;
```

### 3. Утилиты конфигурации (`src/widgets/WorkGroupEPC/lib/paginationConfigHelper.ts`)
```typescript
// Единственная функция - определяет нужна ли пагинация для узла
export const shouldUsePaginationForNode = (node: TreeElement): boolean => {
  if (!node.isDirectory) return false;
  
  if (typeof node.totalCountOfLeafs === 'number') {
    return node.totalCountOfLeafs > PAGINATION_THRESHOLD;
  }
  
  if (typeof node.childrenCount === 'number') {
    return node.childrenCount > PAGINATION_THRESHOLD;
  }
  
  return false;
};
```

### 4. API утилиты (`src/shared/lib/paginatedApiHelpers.ts`)
```typescript
// Создает URL для пагинированного запроса
export const createPaginatedTreeUrl = (
  endpoint: string,
  baseParams: { cabinetId: string; itemId?: string; pos?: string; isCabinet?: boolean },
  page?: number,
  pageSize?: number,
): string

// Определяет есть ли еще элементы для загрузки
export const hasMoreItems = (itemsCount: number, pageSize: number): boolean
```

### 5. Хук с пагинацией (`src/shared/model/useLazyTreeDataWithPagination.ts`)
Упрощенная версия хука `useLazyTreeData` с добавлением:
- `loadMoreItems: LoadMoreCallback` - функция для загрузки дополнительных элементов
- `paginationState: TreePaginationState` - состояние пагинации всех узлов
- `resetPagination: (nodeKey?: string) => void` - сброс состояния пагинации

### 6. Кнопка "Загрузить еще" (`src/shared/ui/LoadMoreButton/index.tsx`)
Компонент кнопки с тремя вариантами отображения:
- `default` - обычная кнопка
- `compact` - компактная версия
- `minimal` - минимальная версия

### 7. Узел с пагинацией (`src/widgets/WorkGroupEPC/ui/NodeWithButtonsAndPagination/index.tsx`)
Расширяет `NodeWithButtons` добавлением кнопки "Загрузить еще" когда:
- Узел является директорией
- У узла есть состояние пагинации с `hasMore: true`
- Узел не находится в состоянии загрузки

### 8. Хук EPC дерева с пагинацией (`src/widgets/WorkGroupEPC/store/hooks/useEPCTreeWithPagination.ts`)
Упрощенная версия `useEPCTree` с добавлением объекта `pagination`:
```typescript
pagination: {
  loadMoreItems: LoadMoreCallback;
  getPaginationInfo: (nodeKey: string) => NodePaginationInfo | undefined;
  resetPagination: (nodeKey?: string) => void;
}
```

## Расширение TreeElement

В `src/react-app-env.d.ts` добавлены свойства пагинации:
```typescript
interface TreeElement {
  // ... существующие свойства
  hasMore?: boolean;
  currentPage?: number;
  totalPages?: number;
  isLoadingMore?: boolean;
  loadedItemsCount?: number;
}
```

## Как это работает

1. **Определение необходимости пагинации**: При загрузке узла проверяется количество дочерних элементов через `shouldUsePaginationForNode`

2. **Первоначальная загрузка**: Загружается первая страница с размером `PAGINATION_THRESHOLD`

3. **Определение наличия дополнительных элементов**: Если количество полученных элементов равно размеру страницы, предполагается наличие дополнительных элементов

4. **Отображение кнопки**: Если `hasMore: true`, отображается кнопка "Загрузить еще"

5. **Загрузка дополнительных элементов**: При нажатии кнопки вызывается `loadMoreItems`, который загружает следующую страницу и добавляет элементы к существующим

## Использование

```typescript
// В компоненте EPC дерева
const treeHook = useEPCTreeWithPagination(cabinetId, togglePopup, true);

// Получение информации о пагинации узла
const paginationInfo = treeHook.pagination.getPaginationInfo(nodeKey);

// Загрузка дополнительных элементов
await treeHook.pagination.loadMoreItems(nodeKey);

// Сброс пагинации
treeHook.pagination.resetPagination(nodeKey);
```

## Тестирование

Базовые тесты находятся в `src/widgets/WorkGroupEPC/tests/basicPaginationTest.ts`:
```typescript
import { runBasicPaginationTests } from './tests/basicPaginationTest';

// Запуск тестов
runBasicPaginationTests();
```

## Ключевые особенности упрощенной реализации

1. **Минимум конфигурации**: Только одна константа `PAGINATION_THRESHOLD`
2. **Простая логика**: Эвристическое определение наличия дополнительных элементов
3. **Без оптимизаций**: Убраны сложные системы кэширования и мониторинга производительности
4. **Обратная совместимость**: Работает с существующим API без изменений на бэкенде
5. **Понятный код**: Простая и читаемая реализация без избыточной сложности

## Ограничения

1. Не поддерживает точное количество страниц (только hasMore)
2. Нет продвинутых оптимизаций производительности
3. Простая эвристика для определения наличия дополнительных элементов
4. Нет детального мониторинга и отладки

Эта реализация обеспечивает базовую функциональность "Загрузить еще" с минимальной сложностью и максимальной понятностью кода.
