import { DEBUG_PAGINATION, PAGINATION_LOAD_DELAY } from '../config/constants';

/**
 * Кэш для хранения результатов вычислений конфигурации узлов
 */
const nodeConfigCache = new Map<string, any>();

/**
 * Кэш для хранения результатов определения типа узла
 */
const nodeTypeCache = new Map<string, string>();

/**
 * Кэш для хранения результатов проверки необходимости пагинации
 */
const paginationNeedCache = new Map<string, boolean>();

/**
 * Максимальный размер кэша (предотвращает утечки памяти)
 */
const MAX_CACHE_SIZE = 1000;

/**
 * Очищает кэш если он превышает максимальный размер
 */
const cleanupCache = (cache: Map<string, any>) => {
  if (cache.size > MAX_CACHE_SIZE) {
    // Удаляем первые 20% записей (FIFO)
    const keysToDelete = Array.from(cache.keys()).slice(0, Math.floor(MAX_CACHE_SIZE * 0.2));
    keysToDelete.forEach(key => cache.delete(key));
    
    if (DEBUG_PAGINATION) {
      console.log(`[Performance] Cache cleaned up, removed ${keysToDelete.length} entries`);
    }
  }
};

/**
 * Создает уникальный ключ для кэширования на основе свойств узла
 */
const createNodeCacheKey = (node: TreeElement): string => {
  return `${node.key}_${node.isDirectory}_${node.isFixed}_${node.totalCountOfLeafs}_${node.childrenCount}`;
};

/**
 * Кэширующая обертка для функций определения конфигурации узла
 */
export const withNodeConfigCache = <T>(
  fn: (node: TreeElement) => T,
  cache: Map<string, T>
) => {
  return (node: TreeElement): T => {
    const cacheKey = createNodeCacheKey(node);
    
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }
    
    const result = fn(node);
    cache.set(cacheKey, result);
    cleanupCache(cache);
    
    return result;
  };
};

/**
 * Дебаунс функция для предотвращения частых вызовов
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle функция для ограничения частоты вызовов
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Батчинг для группировки множественных операций
 */
export class BatchProcessor<T> {
  private batch: T[] = [];
  private timeoutId: NodeJS.Timeout | null = null;
  private readonly batchSize: number;
  private readonly delay: number;
  private readonly processor: (items: T[]) => void;

  constructor(
    processor: (items: T[]) => void,
    batchSize: number = 10,
    delay: number = 100
  ) {
    this.processor = processor;
    this.batchSize = batchSize;
    this.delay = delay;
  }

  add(item: T): void {
    this.batch.push(item);

    if (this.batch.length >= this.batchSize) {
      this.flush();
    } else if (!this.timeoutId) {
      this.timeoutId = setTimeout(() => this.flush(), this.delay);
    }
  }

  flush(): void {
    if (this.batch.length > 0) {
      this.processor([...this.batch]);
      this.batch = [];
    }

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  clear(): void {
    this.batch = [];
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

/**
 * Менеджер для управления состоянием загрузки с оптимизацией
 */
export class LoadingStateManager {
  private loadingStates = new Map<string, boolean>();
  private loadingPromises = new Map<string, Promise<any>>();
  private readonly debouncedUpdate: (nodeKey: string, isLoading: boolean) => void;

  constructor(updateCallback: (nodeKey: string, isLoading: boolean) => void) {
    this.debouncedUpdate = debounce(updateCallback, 50);
  }

  setLoading(nodeKey: string, isLoading: boolean): void {
    this.loadingStates.set(nodeKey, isLoading);
    this.debouncedUpdate(nodeKey, isLoading);
  }

  isLoading(nodeKey: string): boolean {
    return this.loadingStates.get(nodeKey) || false;
  }

  setPromise(nodeKey: string, promise: Promise<any>): void {
    this.loadingPromises.set(nodeKey, promise);
    
    // Автоматически очищаем промис после завершения
    promise.finally(() => {
      this.loadingPromises.delete(nodeKey);
    });
  }

  getPromise(nodeKey: string): Promise<any> | undefined {
    return this.loadingPromises.get(nodeKey);
  }

  clear(nodeKey?: string): void {
    if (nodeKey) {
      this.loadingStates.delete(nodeKey);
      this.loadingPromises.delete(nodeKey);
    } else {
      this.loadingStates.clear();
      this.loadingPromises.clear();
    }
  }
}

/**
 * Оптимизированный планировщик для загрузки данных
 */
export class LoadScheduler {
  private queue: Array<() => Promise<any>> = [];
  private isProcessing = false;
  private readonly concurrency: number;
  private activePromises = 0;

  constructor(concurrency: number = 3) {
    this.concurrency = concurrency;
  }

  schedule<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await task();
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        }
      });

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.activePromises >= this.concurrency) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.activePromises < this.concurrency) {
      const task = this.queue.shift();
      if (task) {
        this.activePromises++;
        
        task()
          .finally(() => {
            this.activePromises--;
            this.processQueue();
          });
      }
    }

    this.isProcessing = false;
  }

  clear(): void {
    this.queue = [];
  }

  get queueSize(): number {
    return this.queue.length;
  }

  get activeCount(): number {
    return this.activePromises;
  }
}

/**
 * Утилиты для мониторинга производительности
 */
export class PerformanceMonitor {
  private static measurements = new Map<string, number[]>();

  static startMeasurement(key: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.measurements.has(key)) {
        this.measurements.set(key, []);
      }
      
      const measurements = this.measurements.get(key)!;
      measurements.push(duration);
      
      // Ограничиваем количество измерений
      if (measurements.length > 100) {
        measurements.shift();
      }
      
      if (DEBUG_PAGINATION) {
        console.log(`[Performance] ${key}: ${duration.toFixed(2)}ms`);
      }
    };
  }

  static getStats(key: string): { avg: number; min: number; max: number; count: number } | null {
    const measurements = this.measurements.get(key);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const avg = measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
    const min = Math.min(...measurements);
    const max = Math.max(...measurements);

    return { avg, min, max, count: measurements.length };
  }

  static logAllStats(): void {
    console.group('[Performance Stats]');
    for (const [key, measurements] of this.measurements) {
      const stats = this.getStats(key);
      if (stats) {
        console.log(`${key}:`, stats);
      }
    }
    console.groupEnd();
  }

  static clear(key?: string): void {
    if (key) {
      this.measurements.delete(key);
    } else {
      this.measurements.clear();
    }
  }
}

/**
 * Экспорт кэшированных версий функций
 */
export const caches = {
  nodeConfig: nodeConfigCache,
  nodeType: nodeTypeCache,
  paginationNeed: paginationNeedCache,
  clear: () => {
    nodeConfigCache.clear();
    nodeTypeCache.clear();
    paginationNeedCache.clear();
  },
};
