@import 'src/app/styles/mixins';

.head {
  position: relative;
  display: flex;
  width: 100%;
  margin: 0 auto 20px;
  justify-content: space-between;

  @include scrollBar;

  &Toggle {
    position: absolute !important;
    bottom: 9px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3 !important;

    &<PERSON><PERSON> {
      height: 22px !important;
      padding: 0 14px !important;
      width: 49px !important;
    }
  }
}

.container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 10px;
  margin-bottom: 30px;
  border-radius: 4px;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid rgb(128 128 128 / 19%);

  &Small {
    max-height: 92px;
    transition: max-height 700ms cubic-bezier(0, 1, 0, 1);
  }

  &Full {
    max-height: 1000px;
    transition: max-height 1000ms ease-in-out;
  }
}

.spinner {
  padding: 4px 0;
  height: max-content;
  border-bottom: 1px solid rgb(128 128 128 / 19%);
}
