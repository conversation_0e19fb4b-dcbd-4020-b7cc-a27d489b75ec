@import 'src/app/styles/mixins';

.container {
  margin: auto;
  height: auto;
  border-radius: 4px;
}

.download {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-direction: column;
  justify-content: space-between;

  &__content {
    display: flex;
    flex-direction: column;
    gap: 30px;
    @include defaultBorder;

    padding: 20px;
    border-radius: 3px;
  }

  &__radio {
    display: flex;
  }

  &__button {
    width: 10%;
  }

  &DropDown {
    width: min-content !important;
  }

  &Container {
    display: flex;
    width: 100%;
    gap: 20px;
    align-items: center;

    &Text {
      width: 20%;
    }
  }
}
