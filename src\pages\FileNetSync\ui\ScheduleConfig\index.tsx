/* Компонент настройки расписания  */
import { InputNumber, Select, Typography } from 'antd';
import { FC, useState } from 'react';
import { SchedulerRequest } from '../..';
import { labelMapping, typesMapping } from '../../config';
import { pluralAdjectiveForms } from '../../lib';

import { enums } from '../../store';

import styles from './styles.module.scss';

export const ScheduleConfig: FC<{
  children: (props: Pick<SchedulerRequest, 'schedulerSettings'>) => JSX.Element;
}> = ({ children }) => {
  const [schedulerSettings, setSchedulerSettings] = useState<
    SchedulerRequest['schedulerSettings']
  >({ periodValue: '1', periodType: enums.PeriodType.DAYS });

  return (
    <div className={styles.column}>
      <div className={styles.row}>
        <Typography.Text>Расписание: </Typography.Text>
        <Typography.Text>
          {pluralAdjectiveForms(
            labelMapping[schedulerSettings.periodType],
            schedulerSettings.periodValue,
          )}
        </Typography.Text>

        <InputNumber
          maxLength={4000}
          className={styles.scheduleInput}
          value={parseInt(schedulerSettings.periodValue, 10)}
          min={1}
          max={1000}
          onChange={(value) => {
            if (value) {
              setSchedulerSettings((prev) => ({
                ...prev,
                periodValue: String(value),
              }));
            }
          }}
        />

        <Select
          value={schedulerSettings.periodType}
          className={styles.scheduleInput}
          onChange={(value) => {
            setSchedulerSettings((prev) => ({
              ...prev,
              periodType: value,
            }));
          }}
        >
          {Object.entries(enums.PeriodType).map(([key, value]) => (
            <Select.Option value={key} key={value}>
              {pluralAdjectiveForms(
                typesMapping[value],
                schedulerSettings.periodValue,
              )}
            </Select.Option>
          ))}
        </Select>
      </div>

      {children({ schedulerSettings })}
    </div>
  );
};
