export interface ColumnFilters {
  column: string;
  from: string;
  fromNum: string;
  query: string;
  to: string;
  toNum: string;
  values: (boolean | Key)[];
}

export interface ColumnFilterActions {
  filters: ColumnFilters[];
  onReset: FiltersCallback;
  onSubmit: FiltersCallback;
}

export type FiltersInitial = Record<'nested' | 'main', ColumnFilters[]>;

export type FiltersCallback = (
  values: ColumnFilters,
  type: keyof FiltersInitial,
) => void;

export interface InnerRenderProps {
  column: import('features/DataGrid').TableColumnData;
  filters: ColumnFilters[];
  onReset: (value: ColumnFilters) => void;
  onSubmit: (value: ColumnFilters) => void;
}
