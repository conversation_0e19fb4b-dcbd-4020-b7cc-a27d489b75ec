import { notification } from 'antd';
import axios from 'axios';
import { useEffect, useMemo } from 'react';
import { WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';
import { EpcPermissions, SaveFile } from 'widgets/WorkGroupEPC/types';
import { permissionsConfig } from 'entities/Permissions';
import { appErrorNotification } from 'shared/lib';
import { createBasicClosableNotice } from 'shared/model';

const createConflictNotice = (message: string): void =>
  createBasicClosableNotice({
    message: 'Внимание',
    description: message,
  });

export const useCopyModalButtons = (
  node: TreeElement,
  handleClose: Callback,
  selectedTree: TreeElement[],
  isDisabled: boolean,
  handleSave: SaveFile,
  isPending: boolean,
  responseData: string | null,
  refetchNode: (node: TreeElement) => void,
  epcPermissions: EpcPermissions,
): AdditionalButton[] => {
  const createHandleSave =
    ({ flat, items }: { flat: boolean; items: Partial<TreeElement>[] }) =>
    async () => {
      try {
        await handleSave('copyFiles', { items, flat });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (err.response?.data?.error) {
            appErrorNotification(err.response?.data?.error, err);
          }
        } else {
          appErrorNotification('Ошибка при копировании', err as AppError);
        }
      }
      handleClose();
    };

  const { canEditEPCStructure } = epcPermissions;

  const isHaveDirectory = useMemo(
    () => selectedTree.some(({ isDirectory }) => isDirectory),
    [selectedTree],
  );

  useEffect(() => {
    if (responseData === null) {
      return;
    }

    setTimeout(() => {
      refetchNode(node);
    }, WorkGroupEPCConfig.DELAY_FOR_REFETCH);

    if (responseData.length > 0) {
      createConflictNotice(responseData);
    } else {
      notification.success({ message: 'Файлы успешно скопированы' });
    }
  }, [responseData]); // eslint-disable-line

  return [
    {
      title: 'Вставить файлы',
      key: 'saveFiles',
      type: 'primary',
      onClick: createHandleSave({ items: selectedTree, flat: true }),
      disabled: isDisabled,
      loading: isPending,
    },
    {
      title: 'Вставить файлы с каталогами',
      key: 'saveFilesWithCatalogs',
      type: 'primary',
      onClick: createHandleSave({ items: selectedTree, flat: false }),
      disabled: isDisabled || !canEditEPCStructure || !isHaveDirectory,
      tooltip: !canEditEPCStructure
        ? `${permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение структуры каталогов',
          )}`
        : `${
            !isHaveDirectory
              ? 'Не выбрано ни одной директории для копирования'
              : ''
          }`,
      loading: isPending,
    },
    {
      title: 'Отмена',
      key: 'cancel',
      ghost: true,
      danger: true,
      onClick: handleClose,
    },
  ];
};
