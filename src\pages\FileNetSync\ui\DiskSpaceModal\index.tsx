import { FC, useEffect, useState } from 'react';
import { DiskSpace } from 'widgets/DiskSpace';
import { fileStructureStore } from 'entities/FileStructure';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { AppPopup } from 'shared/ui';

import { hooks, selectors } from '../../store';

import styles from './styles.module.scss';

const DiskSpaceHoc: FC = () => {
  const [isStructurePending, setStructurePending] = useState(false);
  const [structureData, setStructureData] = useState<TableColumnsAndRows>({
    columns: [],
    rows: [],
  });
  const { isPending, repos } = useAppSelector(selectors.repositoriesSelector);

  const dispatch = useAppDispatch();

  useEffect(() => {
    setStructurePending(true);
    dispatch(fileStructureStore.actions.getFileStructureThunk())
      .unwrap()
      .then(setStructureData)
      .finally(() => setStructurePending(false));
  }, []); // eslint-disable-line

  return (
    <DiskSpace
      isFileTypesPending={isPending}
      isStructurePending={isStructurePending}
      repos={repos}
      structureData={structureData}
    />
  );
};

export const DiskSpaceModal: FC = () => {
  const [isDiskOpen, , closeDisk] = hooks.useFSyncDiskModal();

  return (
    <AppPopup
      isOpened={isDiskOpen}
      onClose={closeDisk}
      className={styles.popup}
      title="Информация о диске ФХ"
    >
      <DiskSpaceHoc />
    </AppPopup>
  );
};
