import {
  BlockOutlined,
  DeleteTwoTone,
  DownloadOutlined,
  EyeOutlined,
  PartitionOutlined,
  PushpinOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { notification, Skeleton, Typography } from 'antd';
import { ReactNode } from 'react';
import {
  EpcPermissions,
  FilesVisibility,
  LinkedData,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import { permissionsConfig } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import {
  fileDeletionHintsByBinding,
  FileDeletionHintsByBindingKeys,
} from 'shared/config';
import {
  appErrorNotification,
  asyncDownloadFile,
  createNewWindowForApps,
  downloadFile,
  guessNoticeMessage,
  getParentsNodes,
} from 'shared/lib';
import {
  RequestTrigger,
  createConfirmModal,
  normalizeFileName,
} from 'shared/model';
import { ButtonsContainer } from 'shared/ui';
import { LoadMoreButton } from 'shared/ui/LoadMoreButton';
import { shouldUsePaginationForNode } from '../../lib/paginationConfigHelper';
import type { LoadMoreCallback, NodePaginationInfo } from '../../types';

import styles from './styles.module.scss';

const handleGetFile = async (endpoint: string): Promise<void> => {
  const res = await appInstance.get(endpoint, {
    responseType: 'blob',
  });
  downloadFile(res.data, normalizeFileName(res.headers));
};

const DISABLE_KEY = 'disabled';

export const NodeWithButtons = ({
  tree,
  node,
  title,
  togglePopup,
  handleUpdateLinked,
  getLinked,
  refetchNode,
  epcPermissions,
  saveStatus,
  // Новые параметры для пагинации
  onLoadMore,
  paginationInfo,
}: {
  epcPermissions: EpcPermissions;
  getLinked: RequestTrigger<TreeElement[]>;
  handleUpdateLinked: (body: LinkedData) => void;
  node: TreeElement;
  refetchNode: (node: TreeElement) => void;
  saveStatus: FilesVisibility;
  title: ReactNode;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
  tree: TreeElement[];
  // Новые параметры для пагинации
  onLoadMore?: LoadMoreCallback;
  paginationInfo?: NodePaginationInfo;
}): ReactNode => {
  const refetchParentNodes = async (child: TreeElement): Promise<void> => {
    const parents = getParentsNodes(tree, [child.itemId as Key]);
    parents.forEach((parent) => {
      refetchNode(parent);
    });
  };

  const buttons: AdditionalButton[] = [
    {
      title: '',
      icon: <DownloadOutlined />,
      key:
        epcPermissions.canDownloadFiles && !node.isDirectory
          ? 'download'
          : DISABLE_KEY,
      type: 'dashed',
      size: 'small',
      disabled: !node?.permissions?.canDownload,
      tooltip: !node?.permissions?.canDownload
        ? permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
        : 'Скачать файл',
      onClick: () => {
        if (node.fileNetId && Boolean(node?.permissions?.canDownload)) {
          asyncDownloadFile(
            handleGetFile(
              apiUrls.workGroup.fileData.downloadFileById(node.fileNetId),
            ),
          );
        }
      },
    },
    {
      title: '',
      key:
        epcPermissions.canDownloadFiles &&
        !node.isDirectory &&
        node.fileSignExist
          ? 'downloadWithSign'
          : DISABLE_KEY,
      size: 'small',
      type: 'dashed',
      disabled: !node?.permissions?.canDownload,
      tooltip: !node?.permissions?.canDownload
        ? permissionsConfig.warnMessages.noPermissionsDefault('скачивание')
        : 'Скачать файл с подписью',
      icon: <SafetyOutlined />,
      onClick: () => {
        if (node.fileNetId) {
          asyncDownloadFile(
            handleGetFile(
              apiUrls.workGroup.fileData.downloadFileByIdWithSign(
                node.fileNetId,
              ),
            ),
          );
        }
      },
    },
    {
      title: '',
      key:
        epcPermissions.canViewFiles && !node.isDirectory ? 'view' : DISABLE_KEY,
      size: 'small',
      type: 'dashed',
      disabled: !node?.permissions?.canView,
      icon: <EyeOutlined />,
      onClick: async () => {
        if (node.fileNetId && node?.permissions?.canView) {
          const res = await appInstance.get<{
            data: string;
            success: boolean;
          }>(apiUrls.workGroup.fileData.fileViewer(node.fileNetId));
          guessNoticeMessage('viewer');
          createNewWindowForApps(res.data.data);
        }
      },
      tooltip: !node?.permissions?.canView
        ? permissionsConfig.warnMessages.noPermissionsDefault('просмотр')
        : 'Просмотр в визуализаторе',
    },
    {
      title: '',
      type: 'dashed',
      key:
        node?.isMain === 0 && !node.isDirectory && epcPermissions.canEdit
          ? 'main'
          : DISABLE_KEY,
      size: 'small',
      icon: <PushpinOutlined />,
      onClick: () => {
        createConfirmModal({
          title: 'Отметить основным',
          message: `Отметить основным файл "${node.title}"?`,
          onConfirm: async () => {
            if (node.fileNetId) {
              try {
                await appInstance.get(
                  apiUrls.workGroup.EPC.markMainFile(node.fileNetId),
                );
                await refetchParentNodes(node);
              } catch (error) {
                appErrorNotification(
                  `Произошла ошибка при попытке отметить "${node.title}" основным`,
                  error as AppError,
                );
              }
            }
          },
        });
      },
      tooltip: 'Отметить основным',
    },
    {
      title: '',
      key: node.key !== '0-0' ? 'viewCabinet' : DISABLE_KEY,
      type: 'dashed',
      size: 'small',
      disabled: !epcPermissions.canEdit,
      tooltip: !epcPermissions.canEdit
        ? permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение видимости',
          )
        : `Изменить видимость ${node.isDirectory ? 'директории' : 'файла'}`,
      icon: <BlockOutlined />,
      onClick: () =>
        createConfirmModal({
          title: 'Внимание',
          message: !node.cabinetOnly
            ? `Вы действительно хотите скрыть ${
                node.isDirectory ? 'директорию' : 'файл'
              } для Контроля выполнения?\n` +
              `${node.isDirectory ? 'Директория' : 'Файл'} будет доступ${
                node.isDirectory ? 'на' : 'ен'
              } в режиме "только для КРГ".`
            : `${node.isDirectory ? 'Директория' : 'Файл'} доступ${
                node.isDirectory ? 'на' : 'ен'
              } в режиме "только для КРГ".\n` +
              `Вы действительно хотите отображать ${
                node.isDirectory ? 'директорию' : 'файл'
              } в Контроле выполнения?`,
          onConfirm: async () => {
            try {
              if (node?.itemId) {
                if (node.isDirectory) {
                  await saveStatus([], [node?.itemId], !node.cabinetOnly);
                } else {
                  await saveStatus([node?.itemId], [], !node.cabinetOnly);
                }
                await refetchParentNodes(node);
                notification.success({
                  message: `Доступность ${
                    node.isDirectory ? 'директории' : 'файла'
                  } "${node.title}" успешно изменена`,
                });
              }
            } catch (err) {
              appErrorNotification(
                'Произошла ошибка изменения видимости',
                err as AppError,
              );
            }
          },
        }),
    },
    {
      title: '',
      key:
        epcPermissions.canDeleteFiles && !node.isDirectory
          ? 'delete'
          : DISABLE_KEY,
      type: 'dashed',
      size: 'small',
      disabled: !node.isRemovable,
      tooltip: node.isRemovable
        ? 'Удалить файл'
        : Object.keys(fileDeletionHintsByBinding)
            .reduce(
              (acc, key) => {
                if (key in node && node[key as keyof TreeElement]) {
                  return [
                    ...acc,
                    fileDeletionHintsByBinding[
                      key as FileDeletionHintsByBindingKeys
                    ] || '',
                  ];
                }
                return acc;
              },
              ['Невозможно удалить файл'],
            )
            .filter((hint) => hint)
            .join('. '),
      icon: (
        <DeleteTwoTone
          twoToneColor={!node.isRemovable ? '#ABABAB' : '#FF4D4F'}
        />
      ),
      onClick: () => {
        createConfirmModal({
          title: 'Внимание',
          message: `Вы действительно хотите удалить "${node.title}"?`,
          onConfirm: async () => {
            if (node.fileNetId) {
              await appInstance
                .delete(
                  apiUrls.workGroup.fileData.deleteFileById(node.fileNetId),
                )
                .then(() => {
                  refetchParentNodes(node);
                  notification.success({
                    message: `Файл "${node.title}" успешно удален`,
                  });
                });
            }
          },
        });
      },
    },
    {
      title: '',
      type: 'text',
      tooltip: node?.hasLinked
        ? 'Просмотр связанных каталогов и файлов'
        : 'Редактирование связей',
      icon: <PartitionOutlined />,
      key: node.isDirectory ? `linked-${node.itemId}` : DISABLE_KEY,
      className: styles.rootButton,
      onClick: async () => {
        try {
          const linked = await getLinked(
            apiUrls.workGroup.EPC.getLinkedElements(node.itemId),
          );
          handleUpdateLinked({
            directoryId: node.itemId || '',
            watchLinked: linked,
            editLinked: linked,
            title: node.title,
          });
          togglePopup(
            !node?.hasLinked && epcPermissions.canEdit ? 'edit' : 'watch',
          );
        } catch (error) {
          appErrorNotification(
            'Не удалось получить связанные каталоги и файлы',
            error as AppError,
          );
        }
      },
    },
  ];

  const filteredButtons = buttons.filter(
    (button) => button.key !== DISABLE_KEY,
  );

  if (node.isSkeleton) {
    return (
      <div className={styles.root} style={{ width: `${node.width}%` || '60%' }}>
        <Skeleton active paragraph={false} className={styles.skeleton} />
      </div>
    );
  }

  // Определяем, нужно ли показывать кнопку Load More
  const shouldShowLoadMore =
    node.isDirectory &&
    shouldUsePaginationForNode(node) &&
    paginationInfo?.hasMore &&
    onLoadMore;

  return (
    <div className={styles.root} key={`buttons-${node.itemId}`}>
      <div className={styles.nodeContent}>
        <Typography.Text title="" key={`title-${node.itemId}`}>
          {title}
        </Typography.Text>

        <ButtonsContainer
          key={`${node.itemId}-buttonContainer`}
          className={styles.rootContainer}
          buttons={filteredButtons}
        />
      </div>

      {shouldShowLoadMore && (
        <div className={styles.loadMoreContainer}>
          <LoadMoreButton
            onLoadMore={() => onLoadMore(String(node.key))}
            loading={paginationInfo?.isLoadingMore || false}
            loadedCount={paginationInfo?.loadedItemsCount}
            size="small"
            variant="compact"
            showCounter
          />
        </div>
      )}
    </div>
  );
};
