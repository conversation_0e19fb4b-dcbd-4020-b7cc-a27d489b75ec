import {
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Form,
  Input,
  Tooltip,
} from 'antd';
import classNames from 'classnames';
import moment, { Moment } from 'moment';
import { FC } from 'react';
import { useEffectOnce } from 'react-use';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { FilenetSearchInitialState, SearchFormProps } from '../..';

import { enums } from '../../config';
import { reducers, selectors } from '../../store';

import styles from './styles.module.scss';

const CHECKBOX_GROUP_FIELD_NAME = 'documentContentSearchTypes';

const AUDIT_PERIOD_KEY = 'auditPeriod';
const AUDIT_PERIOD_FROM_KEY = 'auditPeriodFrom';
const AUDIT_PERIOD_TO_KEY = 'auditPeriodTo';

const CREATE_PERIOD_KEY = 'createPeriod';
const CREATE_PERIOD_FROM_KEY = 'createPeriodFrom';
const CREATE_PERIOD_TO_KEY = 'createPeriodTo';

const MODIFIED_PERIOD_KEY = 'modifiedPeriod';
const MODIFIED_PERIOD_FROM_KEY = 'modifiedPeriodFrom';
const MODIFIED_PERIOD_TO_KEY = 'modifiedPeriodTo';

const UPLOAD_PERIOD_KEY = 'uploadPeriod';
const UPLOAD_PERIOD_FROM_KEY = 'uploadPeriodFrom';
const UPLOAD_PERIOD_TO_KEY = 'uploadPeriodTo';

const periodKeys: Record<
  string,
  Partial<keyof FilenetSearchInitialState['extendedSearch']>[]
> = {
  [AUDIT_PERIOD_KEY]: [AUDIT_PERIOD_FROM_KEY, AUDIT_PERIOD_TO_KEY],
  [CREATE_PERIOD_KEY]: [CREATE_PERIOD_FROM_KEY, CREATE_PERIOD_TO_KEY],
  [MODIFIED_PERIOD_KEY]: [MODIFIED_PERIOD_FROM_KEY, MODIFIED_PERIOD_TO_KEY],
  [UPLOAD_PERIOD_KEY]: [UPLOAD_PERIOD_FROM_KEY, UPLOAD_PERIOD_TO_KEY],
};

export const ExtendedSearch: FC<SearchFormProps> = ({
  onSubmit,
  onClear,
  isPending,
}) => {
  const [form] = Form.useForm();

  const formValues = useAppSelector(selectors.extendedSearchSelector);

  const isExtendedSearchFormEmpty = useAppSelector(
    selectors.isExtendedSearchEmptySelector,
  );

  const { setExtendedSearchValues } = useCreateSliceActions(
    reducers.slice.actions,
  );

  /** Загрузка значений из стейта */
  useEffectOnce(() => {
    const hasValue = (
      field: keyof FilenetSearchInitialState['extendedSearch'],
    ): boolean => Boolean(formValues[field] && formValues[field] !== '');

    const periods: Partial<Record<string, [Moment, Moment]>> = {};

    Object.entries(periodKeys).forEach(([key, [from, to]]) => {
      if (hasValue(from) && hasValue(to)) {
        periods[key] = [
          moment(formValues[from], DEFAULT_DATE_VARIANT),
          moment(formValues[to], DEFAULT_DATE_VARIANT),
        ];
      }
    });

    form.setFieldsValue({
      ...formValues,
      ...periods,
    });
  });

  return (
    <Form
      form={form}
      name="extended-search"
      className={styles.form}
      onFinish={onSubmit}
      onValuesChange={(_, fields) => {
        const copy = { ...fields };

        delete copy[AUDIT_PERIOD_KEY];
        delete copy[CREATE_PERIOD_KEY];
        delete copy[MODIFIED_PERIOD_KEY];
        delete copy[UPLOAD_PERIOD_KEY];

        Object.entries(periodKeys).forEach(([key, [from, to]]) => {
          if (fields[key]) {
            copy[from] = (fields[key][0] as Moment).format(
              DEFAULT_DATE_VARIANT,
            );
            copy[to] = (fields[key][1] as Moment).format(DEFAULT_DATE_VARIANT);
          }
        });

        setExtendedSearchValues(copy);
      }}
      autoComplete="off"
      labelWrap
      labelAlign="left"
    >
      <Divider className={styles.divider}>Параметры проверки</Divider>
      <div className={classNames(styles.row, styles.row_antFix)}>
        <Form.Item name="auditCode" label="Условный код проверки">
          <Input maxLength={4000} />
        </Form.Item>

        <Form.Item name={AUDIT_PERIOD_KEY} label="Период проведения проверки">
          <DatePicker.RangePicker
            format={DATE_VARIANTS_LIST}
            className={styles.input}
          />
        </Form.Item>
      </div>

      <Divider className={styles.divider}>Реквизиты документов</Divider>
      <div className={classNames(styles.row, styles.row_antFix)}>
        <Form.Item name="title" label="Имя файла" colon={false}>
          <Input maxLength={4000} />
        </Form.Item>

        <Form.Item name="contentPath" label="Путь" colon={false}>
          <Input />
        </Form.Item>
      </div>
      <div className={classNames(styles.row, styles.row_antFix)}>
        <Form.Item
          name={CREATE_PERIOD_KEY}
          label="Период создания"
          colon={false}
        >
          <DatePicker.RangePicker
            format={DATE_VARIANTS_LIST}
            className={styles.input}
          />
        </Form.Item>

        <Form.Item
          name={MODIFIED_PERIOD_KEY}
          label="Период изменения"
          colon={false}
        >
          <DatePicker.RangePicker
            format={DATE_VARIANTS_LIST}
            className={styles.input}
          />
        </Form.Item>

        <Form.Item
          name={UPLOAD_PERIOD_KEY}
          label="Период загрузки"
          colon={false}
        >
          <DatePicker.RangePicker
            format={DATE_VARIANTS_LIST}
            className={styles.input}
          />
        </Form.Item>
      </div>

      <Divider className={styles.divider}>Содержание файла</Divider>
      <div className={classNames(styles.row, styles.row_antFix)}>
        <Form.Item name="documentContent" label="Найти текст" colon={false}>
          <Input />
        </Form.Item>
      </div>

      <div className={styles.row}>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, nextValues) => {
            const prevSet = new Set(prevValues[CHECKBOX_GROUP_FIELD_NAME]);
            const nextSet = new Set(nextValues[CHECKBOX_GROUP_FIELD_NAME]);

            return prevSet.size !== nextSet.size;
          }}
        >
          {() => {
            const SEARCH_TYPES_COUNT = Object.entries(
              enums.DocumentContentSearchTypes,
            ).length;

            const fieldValue: enums.DocumentContentSearchTypes[] =
              form.getFieldValue(CHECKBOX_GROUP_FIELD_NAME) || [];
            const indeterminate =
              fieldValue.length < SEARCH_TYPES_COUNT && fieldValue.length > 0;

            return (
              <>
                <Checkbox
                  indeterminate={indeterminate}
                  checked={fieldValue.length === SEARCH_TYPES_COUNT}
                  onChange={(event) => {
                    const documentContentSearchTypes = event.target.checked
                      ? Object.keys(enums.DocumentContentSearchTypes)
                      : [];

                    setExtendedSearchValues({
                      ...form.getFieldsValue(),
                      documentContentSearchTypes,
                    });
                    form.setFieldValue(
                      CHECKBOX_GROUP_FIELD_NAME,
                      documentContentSearchTypes,
                    );
                  }}
                >
                  Везде
                </Checkbox>

                <Form.Item
                  label="Найти текст"
                  name={CHECKBOX_GROUP_FIELD_NAME}
                  noStyle
                >
                  <Checkbox.Group>
                    <Checkbox
                      value={enums.DocumentContentSearchTypes.IN_DOCUMENT_NAME}
                    >
                      В наименовании карточки
                    </Checkbox>
                    <Checkbox
                      value={enums.DocumentContentSearchTypes.IN_FILE_NAME}
                    >
                      В названиях файлов
                    </Checkbox>
                    <Checkbox
                      value={enums.DocumentContentSearchTypes.IN_FILE_CONTENT}
                    >
                      В содержимом файлов
                    </Checkbox>
                  </Checkbox.Group>
                </Form.Item>
              </>
            );
          }}
        </Form.Item>

        <div className={styles.buttonsContainer}>
          <Tooltip
            title={
              isExtendedSearchFormEmpty
                ? 'Для поиска в расширенном режиме необходимо заполнить хотя бы одно из полей!'
                : ''
            }
          >
            <Button
              disabled={isExtendedSearchFormEmpty}
              type="primary"
              htmlType="submit"
              loading={isPending}
            >
              Найти
            </Button>
          </Tooltip>
          <Button
            loading={isPending}
            type="primary"
            danger
            ghost
            onClick={() => {
              onClear();
              form.resetFields([
                'auditCode',
                'auditPeriod',
                'title',
                'contentPath',
                'createPeriod',
                'modifiedPeriod',
                'uploadPeriod',
                'documentContent',
                'documentContentSearchTypes',
              ]);
            }}
          >
            Очистить
          </Button>
        </div>
      </div>
    </Form>
  );
};
