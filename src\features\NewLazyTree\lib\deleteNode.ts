import { TreeState } from '../types';

// Функция для удаления узла и его потомков
export const deleteNode = (itemId: string, tree: TreeState): void => {
  const item = tree.entities[itemId];
  if (!item) return;

  // Рекурсивное удаление дочерних узлов
  if (item.isDirectory && item.childrenIds) {
    item.childrenIds.forEach((childId) => deleteNode(childId, tree));
  }

  // Удаление узла из списка дочерних элементов родителя
  if (item.parent) {
    const parentNode = tree.entities[item.parent];
    if (parentNode) {
      parentNode.childrenIds = parentNode.childrenIds.filter(
        (id) => id !== itemId,
      );
      parentNode.childrenCount = parentNode.childrenIds.length;
      parentNode.isLeaf =
        parentNode.childrenCount === 0 || !parentNode.isDirectory;
    }
  }

  // Удаление самого узла
  delete tree.entities[itemId];
  // Удаляем ключи состояния ноды
  if (item.isDirectory) {
    delete tree.loadedKeys[itemId];
    delete tree.expandedKeys[itemId];
    delete tree.checkedKeys[itemId];
  }
};
