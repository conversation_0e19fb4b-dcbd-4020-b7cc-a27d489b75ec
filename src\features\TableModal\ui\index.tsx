import { ConfigProvider, Table } from 'antd';
import localeRu from 'antd/lib/locale/ru_RU';
import compose from 'compose-function';
import type { FC } from 'react';
import { useEffect, useMemo, useState } from 'react';

// eslint-disable-next-line boundaries/element-types
import { DataGrid, TableRowData } from 'features/DataGrid';
import { generateUrlWithQueryParams } from 'shared/lib';
import { renderPopup, RenderPopupProps } from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';
import { ApiContainer } from 'shared/ui/ApiContainer';

import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import type { TableModalProps, CheckboxTableProps } from '..';

import styles from './styles.module.scss';

const CheckboxTable: FC<CheckboxTableProps> = ({
  onClose,
  endpoint,
  onSave,
  selectedRows = [],
  filterCheckbox,
  customButtons,
  hideKeys = [],
  hideRowIds = [],
  additionalParams = {},
  tableData = { rows: [], columns: [] },
}) => {
  const [trigger, { isPending, error, data = { columns: [], rows: [] } }] =
    useAxiosRequest<TableColumnsAndRows>();

  const [isSavePending, setSavePending] = useState(false);

  /* Массив с выбранными рядами */
  const [selectedKeys, setSelectedKeys] = useState<Key[]>(
    selectedRows.map((row) => row?.key),
  );

  const filteredRows = useMemo(() => {
    const handleFilterKeys = (rows: TableRowData[]): TableRowData[] => {
      if (hideKeys?.length === 0) return rows;

      return rows.filter((row) => {
        if (row.children) {
          row.children = handleFilterKeys(row.children);
        }

        return !hideKeys?.includes(row?.key);
      });
    };
    const handleFilterRowIds = (rows: TableRowData[]): TableRowData[] => {
      if (hideRowIds?.length === 0) return rows;

      return rows.filter((row) => {
        if (row.children) {
          row.children = handleFilterRowIds(row.children);
        }

        return row?.rowId?.rowId && !hideRowIds?.includes(row.rowId.rowId);
      });
    };

    const widthFilters = compose(handleFilterRowIds, handleFilterKeys);

    return widthFilters(data?.rows || tableData.rows || []);
  }, [data?.rows, hideKeys, hideRowIds, tableData.rows]);

  const [selectedRecords, setSelectedRecords] = useState<TableRowData[]>([
    ...selectedRows,
  ]);

  useEffect(() => {
    if (endpoint) {
      trigger(generateUrlWithQueryParams(endpoint, additionalParams));
    }
  }, []); // eslint-disable-line

  return (
    <ApiContainer
      error={Boolean(filteredRows.length === 0 || error)}
      isPending={isPending}
      errorStatus={filteredRows.length === 0 ? 404 : 500}
      errorTitle={
        filteredRows.length === 0
          ? 'Данные не найдены или уже добавлены к правам'
          : undefined
      }
    >
      <DataGrid
        columns={data?.columns || tableData.columns || []}
        rows={filteredRows}
        searchProps={{ show: true }}
        tableAdditionProps={{
          pagination: {
            pageSize: 8,
            showSizeChanger: false,
            position: ['bottomLeft'],
          },
          scroll: { y: 344, x: 50 },
          size: 'small',
          rowSelection: {
            columnWidth: 80,
            selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
            selectedRowKeys: [...selectedKeys],
            onSelect: (record, selected) => {
              setSelectedRecords((prevState) => {
                if (!selected) {
                  return prevState.filter((item) => item?.key !== record?.key);
                }
                return [...prevState, record];
              });
              setSelectedKeys((prev) => {
                if (selected) {
                  return [...new Set([...prev, record?.key])];
                }

                const set = new Set(prev);
                set.delete(record?.key);
                return [...set];
              });
            },
            onChange: (allSelectedKeys, records, params) => {
              if (params.type === 'all' && allSelectedKeys.length > 0) {
                setSelectedKeys([...allSelectedKeys]);
                setSelectedRecords([...records]);
              }

              if (
                (params.type === 'all' && allSelectedKeys.length === 0) ||
                params.type === 'none'
              ) {
                setSelectedKeys([]);
                setSelectedRecords([]);
              }
            },
          },
        }}
        filterCheckbox={filterCheckbox}
      />

      <ButtonsContainer
        className={styles.buttons}
        buttons={
          customButtons ?? [
            {
              type: 'primary',
              title: 'Назначить',
              key: 'set',
              disabled: selectedKeys.length === 0 || isPending || isSavePending,
              tooltip:
                selectedKeys.length === 0
                  ? 'Необходимо выбрать элементы таблицы'
                  : undefined,
              onClick: async () => {
                setSavePending(true);

                try {
                  const isSuccess = await onSave(selectedRecords);
                  if (isSuccess) {
                    onClose();
                  }
                } finally {
                  setSavePending(false);
                }
              },
            },
            {
              danger: true,
              title: 'Выйти',
              key: 'exit',
              ghost: true,
              onClick: onClose,
            },
          ]
        }
      />
    </ApiContainer>
  );
};

export const createTableModal = (
  title: string,
  props: TableModalProps,
): void => {
  const options: RenderPopupProps = {
    className: styles.popup,
    title,
    popupUI: ({ onClose }) => (
      <ConfigProvider locale={localeRu}>
        <CheckboxTable onClose={onClose} {...props} />
      </ConfigProvider>
    ),
  };

  renderPopup(options);
};
