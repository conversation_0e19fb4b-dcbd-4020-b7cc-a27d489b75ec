@import 'src/app/styles/mixins';

.tree {
  width: 100%;
  flex-grow: 1;
  overflow: auto;

  @include scrollBar;
  @include customTreeTitleWidth;

  :global(.ant-tree-list-holder) {
    @include scrollBar;
  }

  @include virtualScrollbar;
}

.dropForbidden {
  :global(.ant-tree-drop-indicator) {
    background-color: #ff7d7d !important;
  }

  :global(.ant-tree-drop-indicator::after) {
    border: 2px solid #ff7d7d !important;
  }
}
