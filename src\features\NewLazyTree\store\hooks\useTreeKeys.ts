import { TreeProps } from 'antd';
import { useCallback } from 'react';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { slice } from '../reducer';
import {
  checkedKeysSelector,
  expandedKeysSelector,
  loadedKeysSelector,
  selectedIdSSelector,
} from '../selectors';

// Хук для управления состоянием узлов
export const useTreeKeys = (treeKey: string): typeof treeProps => {
  const { setCheckedKeys, setExpandedKeys, setSelectedNodeId } =
    useCreateSliceActions(slice.actions);

  const selectedKeys = useAppSelector((state) =>
    selectedIdSSelector(state, { treeKey }),
  );
  const loadedKeys = useAppSelector((state) =>
    loadedKeysSelector(state, { treeKey }),
  );
  const expandedKeys = useAppSelector((state) =>
    expandedKeysSelector(state, { treeKey }),
  );
  const checkedKeys = useAppSelector((state) =>
    checkedKeysSelector(state, { treeKey }),
  );

  const onCheck: TreeProps<TreeElement>['onCheck'] = useCallback(
    (_, { node, checked }) => {
      setCheckedKeys({
        checked,
        nodeId: node.itemId!,
        treeKey,
      });
    },
    [setCheckedKeys, treeKey],
  );

  const onExpand: TreeProps<TreeElement>['onExpand'] = useCallback(
    (_, info) => {
      setExpandedKeys({
        nodeIds: [info.node.itemId!],
        isExpanded: info.expanded,
        treeKey,
      });
    },
    [setExpandedKeys, treeKey],
  );

  const onSelect: TreeProps<TreeElement>['onSelect'] = useCallback(
    (keys) => {
      setSelectedNodeId({ treeKey, nodeId: keys[0] });
    },
    [setSelectedNodeId, treeKey],
  );
  const treeProps = {
    loadedKeys,
    expandedKeys,
    checkedKeys,
    selectedKeys,
    onCheck,
    onExpand,
    onSelect,
  };

  return treeProps;
};
