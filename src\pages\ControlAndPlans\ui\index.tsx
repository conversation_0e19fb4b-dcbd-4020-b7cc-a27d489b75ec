import { FC, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import {
  DefaultTableInfoTab,
  RenderTreeProps,
} from 'widgets/DefaultTableInfoTab';
import { FiltersDrawer, filtersDrawerStore } from 'widgets/FiltersDrawer';
import { LazyTreeWithPagination } from 'widgets/LazyTreeWithPagination';
import { NestedLazyTable } from 'widgets/NestedLazyTable';
import {
  SmartGridHandler,
  smartGridHandlerModule,
} from 'widgets/SmartGridHandler';
import {
  DrillFiltersToNestedTable,
  SmartGridTab,
  smartGridTabConfig,
  smartGridTabStore,
} from 'widgets/SmartGridTab';
import { dataGridStore, TableColumnData } from 'features/DataGrid';
import { createDownloadModal } from 'features/DownloadModal';
import { FiltersConfigFileControls } from 'features/FiltersConfigFileControls';
import { createTableConfigModal } from 'features/TableConfigModal';
import {
  ClosableTabsWithMain,
  closableTabsWithMainStore,
  closableTabsWithMainConfig,
} from 'entities/ClosableTabsWithMain';
import { EditedRowLabel } from 'entities/EditedRowLabel';
import { apiUrls } from 'shared/api';
import {
  createConfirmModal,
  useAppSelector,
  useCreateSliceActions,
  usePopupsToggle,
} from 'shared/model';
import { ApiContainer } from 'shared/ui/ApiContainer';
import { PageContainer } from 'shared/ui/PageContainer';
import { ControlAndPlansProps } from '..';
import { getFiltersInvariants, isLazyReport } from '../config';
import { LoadingStatusBox } from './LoadingStatusBox';

const renderNestedTable: DrillFiltersToNestedTable =
  (parentColumns, filters = []) =>
  (endpoint, groupValue, parentRow, onRowClick) =>
    (
      <NestedLazyTable
        endpoint={endpoint}
        groupValue={groupValue}
        parentRow={parentRow}
        renderCustomRow={(row) => <EditedRowLabel row={row} />}
        onRowClick={onRowClick}
        filters={filters}
        parentColumns={parentColumns}
      />
    );

const renderTreeTab = ({
  className,
  treeData,
  tableEndpoint,
  folderEndpoint,
}: RenderTreeProps): JSX.Element => (
  <LazyTreeWithPagination
    title="Файлы проверки"
    className={className}
    treeData={treeData}
    folderEndpoint={apiUrls.defaultPages.treeFolders(folderEndpoint)}
    tableEndpoint={apiUrls.defaultPages.treeFiles(tableEndpoint)}
  />
);

export const ControlAndPlans: FC<ControlAndPlansProps> = ({
  title,
  tableEndpoint,
  isSmartGrid,
  defaultGroupValue,
}) => {
  const [isFirstLoad, setFirstLoad] = useState(true);
  const { DEFAULT_PAGE_SIZE, FIRST_PAGE, EMPTY_GROUPING_VALUE } =
    smartGridTabConfig.constants;

  const {
    selectedFiltersCount,
    selectedFilters,
    disabledFilters,
    lastCachedUrl,
    isPending: isFiltersPending,
    error: filtersError,
  } = useAppSelector(filtersDrawerStore.selectors.selectedFiltersApiSelector);

  /** Хук для очистки фильтров */
  const { clearFilters } = useCreateSliceActions(
    filtersDrawerStore.reducers.slice.actions,
  );

  /** Экшены попапа */
  const [{ filter }, toggle] = usePopupsToggle({
    filter: false,
    config: false,
  } as const);
  const [isSmartGrouped, groupValue, setSmartGrouped] =
    smartGridHandlerModule.useSmartGrouped(tableEndpoint);

  const [tableData, tableState, getTableData, resetTableData] =
    smartGridTabStore.hooks.useTableData(tableEndpoint);

  const {
    currentSelectedProfile,
    updatedColumns,
    tableSizes,
    onResize,
    onProfileSet,
  } = dataGridStore.hooks.useTableProfile(tableData.columns, tableEndpoint);

  const reportColumns: Partial<TableColumnData>[] = useMemo(
    () =>
      updatedColumns.map(({ key, title: columnTitle }) => ({
        key,
        width: tableSizes[String(columnTitle)] || tableSizes[key!] || 150,
      })),
    [tableSizes, updatedColumns],
  );

  const [activeKey, onTabClick, addTab, additionalTabs, closeTab] =
    closableTabsWithMainStore.hooks.useTabs(tableEndpoint);

  /** Хук для получения данных из квери парамсов */
  const [
    currentPage,
    pageSize,
    sortColumnId,
    sortOrder,
    setPage,
    setSort,
    resetQuery,
  ] = smartGridTabStore.hooks.useStateWithQuery();

  /** Получение данных при загрузке страницы */
  useLayoutEffect(() => {
    if (
      lastCachedUrl === apiUrls.defaultPages.filters(tableEndpoint) &&
      isFirstLoad
    ) {
      getTableData(selectedFilters)({
        page: currentPage,
        size: pageSize,
        sort: sortColumnId,
        direction: sortOrder,
        isFirstRequest: isFirstLoad,
      });
      setFirstLoad(false);
    }
  }, [tableEndpoint, selectedFilters, lastCachedUrl, isFirstLoad]); // eslint-disable-line

  /** Ресет при смене ендпоинта */
  useEffect(() => {
    resetTableData();
    setFirstLoad(true);
  }, [tableEndpoint]); // eslint-disable-line

  return (
    <PageContainer containerKey={tableEndpoint}>
      <AppHeader title={title}>
        <LoadingStatusBox />
        <FiltersConfigFileControls
          hidden={activeKey !== closableTabsWithMainConfig.FIRST_TAB}
          isFiltersDisabled={
            tableState.isPending || isSmartGrouped || isFiltersPending
          }
          isReportsDisabled={tableData.rows.length === 0}
          isAllButtonsDisabled={tableState.error !== null}
          handlers={{
            openConfig: () => {
              createTableConfigModal({
                endpoint: tableEndpoint,
                tableSizes,
                columns: tableData.columns,
                onProfileSet: async (profile) => {
                  onProfileSet(profile);

                  if (profile?.sortValue) {
                    await setSort(
                      profile?.sortValue?.value || undefined,
                      profile?.sortValue?.columnUuid,
                    );

                    if (
                      lastCachedUrl ===
                      apiUrls.defaultPages.filters(tableEndpoint)
                    ) {
                      getTableData(selectedFilters)({
                        page: currentPage,
                        size: pageSize,
                        sort: profile?.sortValue?.columnUuid,
                        direction: profile?.sortValue?.value,
                        isFirstRequest: false,
                      });
                    }
                  }
                },
                currentSelectedProfile,
              });
            },
            openDownload: () =>
              createDownloadModal({
                url: tableEndpoint,
                isExportToExcel: true,
                isLazyReport: isLazyReport(tableEndpoint),
                reportControlDTO: {
                  inputData: selectedFilters,
                  reportSettings: {},
                  columns: reportColumns,
                  idAndOrders:
                    Boolean(sortColumnId) && Boolean(sortOrder)
                      ? [
                          {
                            columnId: sortColumnId,
                            descending: sortOrder === 'DESC',
                          },
                        ]
                      : [],
                },
              }),
            clearFilters: createConfirmModal({
              isCallback: true,
              title: 'Очистка',
              message: 'Вы действительно хотите очистить фильтры?',
              onConfirm: () => {
                clearFilters({ clearApplyFilters: true });
                getTableData(disabledFilters)({ page: 1, size: pageSize });
              },
            }) as Callback,
            openFilters: () => toggle('filter'),
            refetchTable: () =>
              getTableData(selectedFilters)({
                page: currentPage,
                size: pageSize,
                sort: sortColumnId,
                direction: sortOrder,
              }),
          }}
          selectedFiltersCount={selectedFiltersCount}
        />
      </AppHeader>

      <ApiContainer
        error={tableState.error || filtersError}
        isPending={
          (tableState.isPending && !tableState.isResEnd) || isFiltersPending
        }
      >
        <ClosableTabsWithMain
          activeKey={activeKey}
          additionalTabs={additionalTabs}
          onClose={closeTab}
          onTabClick={onTabClick}
          MainTab={
            <SmartGridTab
              tableSizes={tableSizes}
              onResize={onResize}
              group={{
                isGrouped: isSmartGrouped,
                groupValue:
                  groupValue || defaultGroupValue || EMPTY_GROUPING_VALUE,
              }}
              pageSize={pageSize}
              sort={{ sortColumnId, sortOrder }}
              onSort={async (newSortOrder, newSortColumnId) => {
                await setSort(newSortOrder, newSortColumnId);
                await getTableData(selectedFilters)({
                  page: currentPage,
                  size: pageSize,
                  sort: newSortColumnId,
                  direction: newSortOrder,
                });
              }}
              onTabAddition={(rowId, tabName, isDoubleClick) =>
                addTab(
                  <DefaultTableInfoTab
                    renderTreeTab={renderTreeTab}
                    tableEndpoint={tableEndpoint}
                    rowId={rowId}
                  />,
                )(rowId, tabName, isDoubleClick)
              }
              columns={isSmartGrouped ? tableData.columns : updatedColumns}
              rows={tableData.rows}
              isPending={tableState.isPending}
              onPagination={async (newPage, newPageSize) => {
                await setPage(newPage, newPageSize);

                if (!isSmartGrouped) {
                  await getTableData(selectedFilters)({
                    page: newPage,
                    size: newPageSize,
                    sort: sortColumnId,
                    direction: sortOrder,
                  });
                }
              }}
              total={tableData.pagination.total}
              footerAdditionalComponent={
                isSmartGrid && (
                  <SmartGridHandler
                    isPending={tableState.isPending}
                    endpoint={tableEndpoint}
                    withSelect={
                      typeof isSmartGrid === 'boolean'
                        ? false
                        : isSmartGrid.withSelect
                    }
                    onChange={async (isGrouped, selectValue) => {
                      await resetQuery();
                      if (isGrouped) {
                        await getTableData(selectedFilters)({
                          group: selectValue || EMPTY_GROUPING_VALUE,
                          page: FIRST_PAGE,
                          size: DEFAULT_PAGE_SIZE,
                        });
                      } else {
                        await getTableData(selectedFilters)({
                          page: FIRST_PAGE,
                          size: DEFAULT_PAGE_SIZE,
                        });
                      }
                      setSmartGrouped(isGrouped, selectValue);
                    }}
                  />
                )
              }
              page={currentPage}
              endpoint={tableEndpoint}
              renderNestedTable={renderNestedTable(
                tableData.columns,
                selectedFilters,
              )}
            />
          }
          title={tableData?.reportTitle || title}
        />
      </ApiContainer>

      <FiltersDrawer
        filtersInvariants={getFiltersInvariants(tableEndpoint)}
        isOpened={filter}
        handleClose={() => toggle('filter', false)}
        filtersEndpoint={apiUrls.defaultPages.filters(tableEndpoint)}
        onSubmit={async (filters) => {
          await resetQuery();
          await getTableData(filters)({
            page: FIRST_PAGE,
            size: DEFAULT_PAGE_SIZE,
            sort: sortColumnId,
            direction: sortOrder,
          });
        }}
      />
    </PageContainer>
  );
};
