import { ReactNode } from 'react';
import {
  EpcPermissions,
  FilesVisibility,
  LinkedData,
  WorkGroupEPCConfig,
  NodePaginationInfo,
  LoadMoreCallback,
} from 'widgets/WorkGroupEPC';
import { RequestTrigger } from 'shared/model';
import { LoadMoreButton } from 'shared/ui';
import { NodeWithButtons } from '../NodeWithButtons';

import styles from './styles.module.scss';

export const NodeWithButtonsAndPagination = ({
  tree,
  node,
  title,
  togglePopup,
  handleUpdateLinked,
  getLinked,
  refetchNode,
  epcPermissions,
  saveStatus,
  // Новые пропы для пагинации
  paginationInfo,
  onLoadMore,
  showLoadMore = true,
}: {
  epcPermissions: EpcPermissions;
  getLinked: RequestTrigger<TreeElement[]>;
  handleUpdateLinked: (body: LinkedData) => void;
  node: TreeElement;
  refetchNode: (node: TreeElement) => void;
  saveStatus: FilesVisibility;
  title: ReactNode;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
  tree: TreeElement[];
  onLoadMore?: LoadMoreCallback;
  // Новые пропы для пагинации
  paginationInfo?: NodePaginationInfo;
  showLoadMore?: boolean;
}): ReactNode => {
  const nodeKey = String(node.key);

  // Определяем, нужно ли показывать кнопку "Load More"
  const shouldShowLoadMore =
    showLoadMore &&
    paginationInfo &&
    paginationInfo.hasMore &&
    node.isDirectory &&
    node.children &&
    node.children.length > 0 &&
    onLoadMore;

  const handleLoadMoreClick = (): void => {
    if (onLoadMore) {
      onLoadMore(nodeKey);
    }
  };

  return (
    <div className={styles.container}>
      {/* Основной компонент с кнопками */}
      <NodeWithButtons
        tree={tree}
        node={node}
        title={title}
        togglePopup={togglePopup}
        handleUpdateLinked={handleUpdateLinked}
        getLinked={getLinked}
        refetchNode={refetchNode}
        epcPermissions={epcPermissions}
        saveStatus={saveStatus}
      />

      {/* Кнопка "Load More" для директорий с пагинацией */}
      {shouldShowLoadMore && (
        <div className={styles.loadMoreContainer}>
          <LoadMoreButton
            onLoadMore={handleLoadMoreClick}
            loading={paginationInfo.isLoadingMore}
            loadedCount={paginationInfo.loadedItemsCount}
            size="small"
            variant="compact"
            showCounter
            className={styles.loadMoreButton}
          />
        </div>
      )}
    </div>
  );
};

export default NodeWithButtonsAndPagination;
