export const DELAY_FOR_REFETCH = 200;
export const IS_CABINET = true;
export const ROOT_KEY = '0-0';

// ============================================================================
// PAGINATION CONSTANTS FOR LOAD MORE FUNCTIONALITY
// ============================================================================

/** Размер страницы по умолчанию для пагинации */
export const DEFAULT_PAGE_SIZE = 10;

/** Максимальный размер страницы */
export const MAX_PAGE_SIZE = 50;

/** Пороговое значение количества файлов, при превышении которого показывается Load More */
export const LOAD_MORE_THRESHOLD = 100;

/** Первая страница (нумерация с 1) */
export const FIRST_PAGE = 1;

// ============================================================================
// PAGINATION CONFIGURATION
// ============================================================================

/** Конфигурация пагинации для разных типов узлов */
export const PAGINATION_CONFIG = {
  /** Настройки для обычных директорий */
  directory: {
    /** Размер страницы по умолчанию */
    defaultPageSize: DEFAULT_PAGE_SIZE,
    /** Пороговое значение для показа Load More */
    threshold: 15,
    /** Максимальный размер страницы */
    maxPageSize: MAX_PAGE_SIZE,
  },
  /** Настройки для системных директорий */
  systemDirectory: {
    /** Размер страницы по умолчанию (больше для системных директорий) */
    defaultPageSize: 20,
    /** Пороговое значение для показа Load More */
    threshold: 25,
    /** Максимальный размер страницы */
    maxPageSize: MAX_PAGE_SIZE,
  },
  /** Настройки для каталогов с большим количеством файлов */
  largeCatalog: {
    /** Размер страницы по умолчанию (меньше для больших каталогов) */
    defaultPageSize: 8,
    /** Пороговое значение для показа Load More */
    threshold: 10,
    /** Максимальный размер страницы */
    maxPageSize: 30,
  },
} as const;

/** Флаг для включения/выключения пагинации глобально */
export const ENABLE_PAGINATION = true;

/** Флаг для включения отладочной информации пагинации */
export const DEBUG_PAGINATION = false;

/** Время задержки перед загрузкой следующей страницы (мс) */
export const PAGINATION_LOAD_DELAY = 300;
