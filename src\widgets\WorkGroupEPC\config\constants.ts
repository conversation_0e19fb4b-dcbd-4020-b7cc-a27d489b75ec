export const DELAY_FOR_REFETCH = 200;
export const IS_CABINET = true;
export const ROOT_KEY = '0-0';

// ============================================================================
// PAGINATION CONSTANTS FOR LOAD MORE FUNCTIONALITY
// ============================================================================

/** Размер страницы по умолчанию для пагинации */
export const DEFAULT_PAGE_SIZE = 10;

/** Максимальный размер страницы */
export const MAX_PAGE_SIZE = 50;

/** Первая страница (нумерация с 1) */
export const FIRST_PAGE = 1;

/** Пороговое значение для показа Load More (количество дочерних элементов) */
export const PAGINATION_THRESHOLD = 15;

/** Флаг для включения пагинации (включено для тестирования) */
export const ENABLE_PAGINATION = true;
