import {
  DeleteOutlined,
  DownloadOutlined,
  FilterOutlined,
  InfoCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Typography } from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { FC } from 'react';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import styles from './styles.module.scss';

type HeaderProps = {
  /** Хендлеры попапов */
  handlers: {
    /** Колбэк на очистку фильтров */
    clearFilters: Callback;
    openConfig: Callback;
    openDownload: Callback;
    openFilters: Callback;
    refetchTable: Callback;
  };
  /** Пропс на дизейбл фильтров */
  isFiltersDisabled: boolean;
  /** Количество выбранных фильтров */
  selectedFiltersCount: number;
  /** Дополнительные кнопки по центру */
  additionalButtons?: AdditionalButton[];
  /** Показывать только заголовок */
  hidden?: boolean;
  /** Дизейбл всех кнопок кроме рефетч */
  isAllButtonsDisabled?: boolean;
  /** Дизейбл кнопоки отчетов */
  isReportsDisabled?: boolean;
};

const parseErrorMessageIfDisabled = (
  isDisabled: boolean,
  additionalMessage: string,
): string => {
  if (isDisabled) {
    return 'Идет получение данных таблицы или таблица сгруппирована';
  }

  return additionalMessage;
};

export const FiltersConfigFileControls: FC<HeaderProps> = ({
  selectedFiltersCount,
  additionalButtons,
  hidden,
  handlers,
  isFiltersDisabled,
  isAllButtonsDisabled,
  isReportsDisabled,
}) => (
  <AnimatePresence>
    {!hidden && (
      <motion.div
        className={styles.header}
        key="not-title"
        initial={{
          height: 0,
          opacity: 0,
        }}
        animate={{
          height: 'auto',
          opacity: 1,
          transition: {
            height: {
              duration: 0.4,
            },
            opacity: {
              duration: 0.25,
              delay: 0.15,
            },
          },
        }}
        exit={{
          height: 0,
          opacity: 0,
          transition: {
            height: {
              duration: 0.4,
            },
            opacity: {
              duration: 0.25,
            },
          },
        }}
      >
        <Typography.Text>
          <InfoCircleOutlined rev="" />
          {selectedFiltersCount <= 0
            ? ' Фильтры не выбраны'
            : ` Для построения таблицы использовано фильтров: ${selectedFiltersCount}`}
        </Typography.Text>

        <div className={styles.buttonsRow}>
          <ButtonsContainer
            buttons={[
              // {
              //   key: 'refresh',
              //   title: 'Обновить',
              //   icon: <RedoOutlined />,
              //   type: 'primary',
              //   tooltip: 'Обновить таблицу результатов',
              //   tooltipProps: {
              //     placement: 'topRight',
              //   },
              //   onClick: handlers.refetchTable,
              // },

              {
                key: 'filters-config',
                title: 'Настроить фильтры',
                onClick: handlers.openFilters,
                icon: <FilterOutlined rev="" />,
                badgeProps: {
                  count: selectedFiltersCount,
                },
                tooltip: parseErrorMessageIfDisabled(
                  isFiltersDisabled,
                  selectedFiltersCount <= 0
                    ? 'Список фильтров пуст'
                    : 'Выбранные фильтры в таблице',
                ),
                tooltipProps: {
                  placement: 'topRight',
                },
                disabled: isFiltersDisabled || isAllButtonsDisabled,
              },

              {
                key: 'filters-clear',
                title: 'Очистить фильтры',
                icon: <DeleteOutlined rev="" />,
                onClick: handlers.clearFilters,
                tooltip: parseErrorMessageIfDisabled(
                  isFiltersDisabled,
                  selectedFiltersCount <= 0
                    ? 'Фильтры не выбраны'
                    : 'Очистить фильтрацию таблицы',
                ),
                ghost: true,
                danger: true,
                tooltipProps: {
                  placement: 'topRight',
                },
                disabled:
                  isFiltersDisabled ||
                  selectedFiltersCount <= 0 ||
                  isAllButtonsDisabled,
              },
            ]}
          />

          {additionalButtons && (
            <ButtonsContainer buttons={additionalButtons} />
          )}

          <ButtonsContainer
            buttons={[
              {
                key: 'config',
                title: 'Настройка таблицы результатов',
                icon: <SettingOutlined rev="" />,
                onClick: handlers.openConfig,
                tooltip: parseErrorMessageIfDisabled(
                  isFiltersDisabled,
                  'Настройки отображения таблицы',
                ),
                tooltipProps: {
                  placement: 'topLeft',
                },
                disabled: isFiltersDisabled || isAllButtonsDisabled,
              },
              {
                key: 'download',
                title: 'Вывести в файл',
                id: '4ae39364-200e-4640-aa3b-f9c0b741e00e',
                type: 'primary',
                icon: <DownloadOutlined rev="" />,
                onClick: handlers.openDownload,
                tooltip: 'Сохранение данных таблицы в файл',
                tooltipProps: {
                  placement: 'topLeft',
                },
                disabled: isAllButtonsDisabled || isReportsDisabled,
              },
            ]}
          />
        </div>
      </motion.div>
    )}
  </AnimatePresence>
  // </div>
);
