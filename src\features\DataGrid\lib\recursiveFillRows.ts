import type { TableRowData } from 'features/DataGrid';

/**
 * Рекурсивно заполняет строки таблицы данными, согласно выбранным значениям
 * чекбоксов
 *
 * @param rows - Массив объектов, представляющих строки таблицы
 * @param checkboxValues - Ключи выбранных чекбоксов
 * @returns Новый массив объектов строк таблицы, заполненный данными согласно
 *   выбранным чекбоксам
 */
export const recursiveFillRows = (
  rows: TableRowData[],
  checkboxValues: string[],
): TableRowData[] =>
  rows.reduce((acc, row) => {
    /* const hasStatusKey = checkboxValues.find(
      (checkboxValue) => row.checkboxStatus?.[checkboxValue],
    );*/

    let hasStatusKey = true;
    /* eslint-disable-next-line */
    for (const checkboxValue of checkboxValues) {
      if (!row.checkboxStatus?.[checkboxValue]) {
        hasStatusKey = false;
      }
    }
    /* http://drpo-jira/browse/APPID-746 fix*/

    if (row.children) {
      const childrenRows = recursiveFillRows(row.children, checkboxValues);
      const hasChildren = childrenRows.length > 0;

      if (hasChildren || hasStatusKey) {
        acc.push({
          ...row,
          children: hasChildren ? childrenRows : undefined,
        });
      }
    } else if (hasStatusKey) {
      acc.push({ ...row, children: undefined });
    }

    return acc;
  }, [] as TableRowData[]);
