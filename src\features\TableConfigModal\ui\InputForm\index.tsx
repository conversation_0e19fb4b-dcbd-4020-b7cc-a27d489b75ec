import { SaveOutlined } from '@ant-design/icons';
import { Button, Checkbox, Input, Tooltip } from 'antd';
import { FC, useState } from 'react';
import { FormValues, InputFormProps } from 'features/TableConfigModal/types';
import { DEFAULT_PROFILE_NAME } from 'shared/config/constants';
import { BorderedFieldset } from 'shared/ui';

import { text } from '../../config';
import styles from './styles.module.scss';

const defaultFormValues: FormValues = {
  inputNameValue: '',
  isDefaultProfile: false,
};

export const InputForm: FC<InputFormProps> = ({
  isPending,
  onSave,
  isError,
}) => {
  const [{ inputNameValue, isDefaultProfile }, setFormValues] =
    useState<FormValues>(defaultFormValues);

  return (
    <BorderedFieldset title={text.formTitle}>
      <form
        className={styles.row}
        onSubmit={async (event) => {
          event.preventDefault();
          await onSave(
            isDefaultProfile ? DEFAULT_PROFILE_NAME : inputNameValue.trim(),
            isDefaultProfile,
          );
          setFormValues(defaultFormValues);
        }}
      >
        <Input
          maxLength={4000}
          placeholder={text.formPlaceHolder}
          prefix={<SaveOutlined />}
          required
          name="newName"
          value={isDefaultProfile ? DEFAULT_PROFILE_NAME : inputNameValue}
          status={isError ? 'error' : ''}
          disabled={isPending || isDefaultProfile}
          onChange={(event) => {
            const value = event.target.value.trim();

            if (DEFAULT_PROFILE_NAME !== value) {
              setFormValues((prev) => ({
                ...prev,
                inputNameValue: event.target.value,
              }));
            }
          }}
        />

        <Checkbox
          className={styles.checkbox}
          checked={isDefaultProfile}
          disabled={isPending}
          onChange={(event) => {
            setFormValues((prev) => ({
              ...prev,
              isDefaultProfile: event.target.checked,
            }));
          }}
        >
          {text.defaultProfileLabel}
        </Checkbox>

        <Tooltip title={text.saveButtonTooltip}>
          <Button
            type="primary"
            htmlType="submit"
            disabled={inputNameValue === '' && !isDefaultProfile}
            loading={isPending}
          >
            {text.saveButtonTitle}
          </Button>
        </Tooltip>
      </form>
    </BorderedFieldset>
  );
};
