import { notification, Tree } from 'antd';
import classNames from 'classnames';
import { memo, useEffect, useRef, useState, type FC } from 'react';
import {
  EpcPermissions,
  FilesVisibility,
  UseTreeTypes,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import {
  getChildrenNodesByParam,
  getParentsNodes,
  handleLazyTreeDnD,
  onDragOver,
} from 'shared/lib';
import { renderTreeTitle } from 'shared/model';
import { ApiContainer } from 'shared/ui';
import { NodeWithButtons } from '../NodeWithButtons';
import styles from './styles.module.scss';

type EPCTreeProps = {
  epcPermissions: EpcPermissions;
  isFullSize: boolean | undefined;
  saveStatus: FilesVisibility;
  togglePopup: (
    name: keyof typeof WorkGroupEPCConfig.popupsInitial,
    status?: boolean | undefined,
  ) => void;
  treeHeight: number;
  // Опциональные пропы для пагинации
  pagination?: {
    getPaginationInfo: (nodeKey: string) => import('../../types').NodePaginationInfo | undefined;
    loadMoreItems: import('../../types').LoadMoreCallback;
    resetPagination: (nodeKey?: string) => void;
  };
} & UseTreeTypes;

export const EPCTree: FC<EPCTreeProps> = memo((props) => {
  const {
    isFullSize,
    treeHeight,
    epcPermissions,
    togglePopup,
    saveStatus,
    tree,
    treeSelect,
    treeDrag,
    treeActions,
    treeCheck,
    treeLoad,
    treeLink,
    treeExpand,
    treeRefs,
    treeArrow,
    // Новые пропы для пагинации (опциональные для обратной совместимости)
    pagination,
  } = props;

  const [dropForbidden, setDropForbidden] = useState(true);

  const nodesCache = useRef(new Map<string, TreeElement[]>());

  // Кешированная функция для получения родителей (так как allowDrop вызывается очень часто)
  const getCachedParentsNodes = (itemId: string): TreeElement[] => {
    if (nodesCache.current.has(itemId)) {
      return nodesCache.current.get(itemId) as TreeElement[];
    }
    const parentsNodes = getParentsNodes(tree.data, [itemId]);
    nodesCache.current.set(itemId, parentsNodes);
    return parentsNodes;
  };

  // Кешированная функция для получения детей (так как allowDrop вызывается очень часто)
  const getCachedChildrenNodes = (itemId: string): TreeElement[] => {
    if (nodesCache.current.has(`${itemId}-children`)) {
      return nodesCache.current.get(`${itemId}-children`) as TreeElement[];
    }
    const childrenNodes = getChildrenNodesByParam(tree.data, itemId);
    nodesCache.current.set(`${itemId}-children`, childrenNodes);
    return childrenNodes;
  };

  // Очистка кеша при изменении структуры дерева и при размонтировании компонента
  useEffect(() => {
    // Функция очистки кэша
    const clearCache = (): void => {
      nodesCache.current.clear();
    };

    // Очистка кэша при изменении структуры дерева
    clearCache();

    // Очистка кэша при размонтировании компонента
    return () => {
      clearCache();
    };
  }, [tree.data]);

  return (
    <ApiContainer
      error={tree.error}
      isPending={tree.isPending}
      className={classNames(styles.popupApi, isFullSize && styles.popupApiFull)}
    >
      <Tree
        ref={treeRefs.treeRef}
        loadedKeys={treeLoad.loadedKeys}
        height={treeHeight} // Включает виртуальный скролл
        checkedKeys={treeCheck.checked.keys}
        onExpand={treeExpand.handleExpand}
        onDragStart={(info) => {
          if (info.node.isDirectory) {
            notification.warn({ message: 'Нельзя переносить директории.' });
          }
          const parentNode = getCachedParentsNodes(info.node.parent!)[0];

          if (parentNode?.isFixed === 2) {
            notification.warn({
              message: 'Нельзя переносить файлы из системной директории.',
            });
          }

          treeDrag.setIsDrag(true);
        }}
        autoExpandParent={treeExpand.autoExpandParent}
        expandedKeys={treeExpand.expandedKeys}
        className={classNames(styles.tree, isFullSize && styles.treeFull, {
          [styles.dropForbidden]: dropForbidden,
        })}
        titleRender={(node: TreeElement) => {
          // Отладочная информация для titleRender
          const paginationInfo = pagination?.getPaginationInfo(String(node.key));
          console.log('EPCTree titleRender:', {
            nodeKey: node.key,
            title: node.title,
            isDirectory: node.isDirectory,
            totalCountOfLeafs: node.totalCountOfLeafs,
            hasPagination: !!pagination,
            paginationInfo,
            hasLoadMoreCallback: !!pagination?.loadMoreItems,
          });

          return NodeWithButtons({
            tree: tree.data,
            node,
            title: (
              <div>
                {renderTreeTitle(
                  node,
                  node.key === treeArrow.foundKey,
                  node.hasLinked,
                  node.key === treeLink.foundLink,
                  true,
                )}
              </div>
            ),
            togglePopup,
            getLinked: treeLink.getLinked,
            handleUpdateLinked: treeLink.handleUpdateLinked,
            refetchNode: treeActions.refetchNode,
            epcPermissions,
            saveStatus,
            // Параметры пагинации (если доступны)
            onLoadMore: pagination?.loadMoreItems,
            paginationInfo,
          });
        }}
        onDragOver={onDragOver}
        treeData={tree.data}
        loadData={treeLoad.onLoadData}
        selectedKeys={treeSelect.selectedValues.selectedKeys}
        defaultExpandAll={false}
        showLine
        selectable
        allowDrop={({ dropNode, dragNode, dropPosition }) => {
          // Запрещаем перетаскивание директорий
          if (dragNode.isDirectory) {
            return false;
          }

          const parentDragNode = getCachedParentsNodes(dragNode.itemId!)[0];

          // Запрещаем, если файл находится в системной директории
          if (parentDragNode?.isFixed === 2) {
            return false;
          }

          if (
            // Запрещаем, если бросаем непосредственно в файл
            dropPosition === 0 &&
            !dropNode.isDirectory
          ) {
            return false;
          }

          if (
            // Если бросаем непосредственно системную директорию
            dropPosition === 0 &&
            dropNode.isFixed === 2
          ) {
            // Подсвечиваем запрещающую линию вставки
            setDropForbidden(true);
            return true;
          }

          // Та же директория, где файл расположен
          // То есть бросаем в gap между элементами в той же директории, где расположен файл
          if (dragNode.parent === dropNode.parent && dropPosition === 1) {
            setDropForbidden(true);
            return true;
          }

          // Если непосредственный родитель
          if (dragNode.parent === dropNode.itemId) {
            setDropForbidden(true);
            return true;
          }

          // Если родитель системная директория
          if (
            dropPosition === 1 &&
            getCachedParentsNodes(dropNode.itemId!)[0]?.isFixed === 2
          ) {
            setDropForbidden(true);
            return true;
          }

          const dropNodeChildren = getCachedChildrenNodes(
            dropPosition === 1 ? dropNode.parent! : dropNode.itemId!,
          );
          const hasDuplicate = dropNodeChildren.find(
            ({ title }) => dragNode.title === title,
          );

          if (hasDuplicate) {
            setDropForbidden(true);
            return true;
          }

          setDropForbidden(false);
          return true;
        }}
        draggable={epcPermissions.canCopyFiles}
        checkable={
          epcPermissions.canCopyFiles ||
          epcPermissions.canDownloadFiles ||
          epcPermissions.canEdit
        }
        checkStrictly
        onCheck={treeCheck.handleCheck}
        onDrop={handleLazyTreeDnD(
          tree.data,
          treeActions.handleMove,
          treeActions.refetchNode,
        )}
        onSelect={(keys, nodes) => {
          if (nodes.nativeEvent.detail === 0) {
            treeSelect.handleReset();
          }
          if (nodes.nativeEvent.detail === 1) {
            treeSelect.handleSelect({
              selectedKeys: keys,
              selectedNode: nodes.node,
            });
          }
        }}
      />
    </ApiContainer>
  );
});
