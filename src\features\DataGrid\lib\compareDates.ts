/*
  Поддерживаемые форматы дат:
  1. ДД.ММ.ГГГГ
  2. ДД-ММ-ГГГГ
  3. ММ.ГГГГ
  4. ММ-ГГГГ
  5. ДД.ММ.ГГГГ чч:мм
  6. ДД-ММ-ГГГГ чч:мм
  7. ДД.ММ.ГГГГ чч:мм:сс
  8. ДД-ММ-ГГГГ чч:мм:сс
*/
const dateRegex =
  // eslint-disable-next-line max-len
  /^(0[1-9]|[12][0-9]|3[01])[.-](0[1-9]|1[0-2])[.-](\d{4})(?:\s(\d{2}):(\d{2})(?::(\d{2}))?)?$|^(0[1-9]|1[0-2])[.-](\d{4})$/;

// Функция для получения временной метки из строки даты
function getTimestamp(parsedDate: RegExpMatchArray): number {
  if (parsedDate[7] && parsedDate[8]) {
    // Формат ММ.ГГГГ
    const date = new Date(
      Number(parsedDate[8]),
      Number(parsedDate[7]) - 1,
      1, // Устанавливаем день как первый день месяца
    );
    return date.getTime();
  }
  // Формат ДД.ММ.ГГГГ
  const date = new Date(
    Number(parsedDate[3]),
    Number(parsedDate[2]) - 1,
    Number(parsedDate[1]),
    Number(parsedDate[4]) || 0,
    Number(parsedDate[5]) || 0,
    Number(parsedDate[6]) || 0,
  );
  return date.getTime();
}

export function compareDates(firstDate: string, secondDate: string): number {
  const firstMatch = firstDate.match(dateRegex);
  const secondMatch = secondDate.match(dateRegex);

  if (firstMatch && secondMatch) {
    const firstTime = getTimestamp(firstMatch);
    const secondTime = getTimestamp(secondMatch);

    return firstTime - secondTime;
  }

  return firstDate.localeCompare(secondDate);
}

export function isValidDate(dateValue: string): boolean {
  return dateRegex.test(dateValue);
}
