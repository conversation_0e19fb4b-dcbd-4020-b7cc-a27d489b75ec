import { useCallback } from 'react';
import { SaveFile, SaveFilesResponse } from 'widgets/WorkGroupEPC';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useCopyFile = (
  cabinetId: string,
  parentId: string,
): [SaveFile, typeof stateSaveFiles] => {
  const [triggerFiles, stateSaveFiles] = useAxiosRequest<SaveFilesResponse>();

  const saveFiles = useCallback<SaveFile>(
    async (type, params) => {
      await triggerFiles(
        generateUrlWithQueryParams(apiUrls.workGroup.EPC.copyFiles, {
          cabinetId,
          parentId,
          flat: params.flat,
        }),
        {
          method: 'POST',
          data: params.items,
        },
      );
    },
    [cabinetId, parentId, triggerFiles],
  );

  return [saveFiles, stateSaveFiles];
};
