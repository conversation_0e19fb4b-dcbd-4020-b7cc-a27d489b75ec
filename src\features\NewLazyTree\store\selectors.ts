import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';
import { getTreeData } from '../lib';
import { slice } from './reducer';

const trees = selectSelf(slice.name);

export const stateSelector = createSelector(
  [trees, (_, props: { treeKey: string }) => props.treeKey],
  (state, treeKey) => state.trees[treeKey],
);

export const entitiesSelector = createSelector(
  [stateSelector],
  (tree) => tree?.entities || {},
);

export const loadedKeysObjSelector = createSelector(
  [stateSelector],
  (tree) => tree?.loadedKeys || {},
);
export const loadedKeysSelector = createSelector(
  [loadedKeysObjSelector],
  (loadedKeys) => Object.keys(loadedKeys || {}) as Key[],
);

export const loadingKeysObjSelector = createSelector(
  [stateSelector],
  (tree) => tree?.loadingKeys || {},
);

export const loadingKeysSelector = createSelector(
  [loadingKeysObjSelector],
  (loadingKeys) => Object.keys(loadingKeys || {}) as Key[],
);

export const rootIdSelector = createSelector(
  [stateSelector],
  (tree) => tree?.rootId,
);

export const selectedNodeIdSelector = createSelector(
  [stateSelector],
  (tree) => tree?.selectedNodeId || '',
);

export const selectedIdSSelector = createSelector(
  [selectedNodeIdSelector],
  (id) => [id],
);

export const treeSelector = createSelector(
  [entitiesSelector, rootIdSelector],
  (entities, rootId) => getTreeData(entities, rootId),
);

export const treeStatusesSelector = createSelector([stateSelector], (tree) => ({
  error: tree?.error,
  isLoading: !!tree?.loading,
  loadingBranch:
    !!tree?.loadingKeys && Object.keys(tree?.loadingKeys)?.length > 0,
}));

export const expandedKeysObjSelector = createSelector(
  [stateSelector],
  (tree) => tree?.expandedKeys,
);
export const expandedKeysSelector = createSelector(
  [expandedKeysObjSelector],
  (keysObj) => Object.keys(keysObj || {}) as Key[],
);

export const checkedKeysObjSelector = createSelector(
  [stateSelector],
  (tree) => tree?.checkedKeys || {},
);

export const checkedKeysSelector = createSelector(
  [checkedKeysObjSelector],
  (keysObj) => Object.keys(keysObj || {}) as Key[],
);

export const checkedKeysWithoutRootSelector = createSelector(
  [checkedKeysSelector, rootIdSelector],
  (checkedKeys, rootId) => checkedKeys.filter((id) => id !== rootId),
);
export const checkedElementsSelector = createSelector(
  [
    entitiesSelector,
    checkedKeysSelector,
    (_, props: { filter: (node: TreeElement) => boolean }) => props.filter,
  ],
  (entities, keys, filter) => {
    const filteredNodes = keys
      .map((key) => entities[key])
      .filter((node): node is TreeElement => !!node && filter(node));
    return filteredNodes;
  },
);
