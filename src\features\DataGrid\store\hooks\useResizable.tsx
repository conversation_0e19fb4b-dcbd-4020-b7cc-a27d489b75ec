import { ColumnType } from 'antd/es/table';
import {
  MouseEvent,
  MutableRefObject,
  SyntheticEvent,
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from 'react';
import { ResizeCallbackData } from 'react-resizable';
import { DataGridProps, TableColumnData, TableRowData } from '../..';
import { constants } from '../../config';

type OnHeaderCell = NonNullable<ColumnType<TableRowData>['onHeaderCell']>;
type OnMouseDown = NonNullable<ReturnType<OnHeaderCell>['onMouseDown']>;
type OnHeaderCellCallback = (index: number) => OnHeaderCell;

const getMultiplier = (dlt: number): number => {
  if (dlt < 0) return 1;

  if (dlt < 5) return 40;
  if (dlt < 10) return 20;
  if (dlt < 20) return 10;
  if (dlt < 30) return 7;
  if (dlt < 50) return 4;
  if (dlt < 100) return 2;
  return 1;
};

export const useResizable = (
  resizableProps: DataGridProps['resizableProps'],
  headerRef: MutableRefObject<HTMLDivElement>,
  columns: TableColumnData[],
): [
  TableColumnData[],
  OnHeaderCellCallback,
  boolean,
  typeof onResizeMove,
  number,
] => {
  const [resizableColumns, setResizableColumns] = useState<TableColumnData[]>(
    [],
  );
  const [isResizeActive, toggleIsResizeActive] = useState(false);
  const [blockLeft, setBlockLeft] = useState(0);
  const [mousePosition, setMousePosition] = useState(0);
  const [tableContainerPositionLeft, setTableContainerPositionLeft] =
    useState(0);

  const onMouseDown = useCallback<OnMouseDown>((event) => {
    if (
      (event?.target as HTMLDivElement)?.className &&
      (event.target as HTMLDivElement).className === 'react-resizable-handle'
    ) {
      const [{ left }] = (
        (event.target as HTMLDivElement).parentNode as HTMLDivElement
      ).getClientRects();

      setBlockLeft(left + constants.MIN_COLUMN_WIDTH);

      toggleIsResizeActive(true);
      setMousePosition(event.clientX);
    }
  }, []);

  const onResizeMove = useCallback(
    (event: MouseEvent<HTMLDivElement>) => {
      setMousePosition(blockLeft > event.clientX ? blockLeft : event.clientX);
    },
    [blockLeft],
  );

  const onResize = useCallback(
    (index) =>
      (_: SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
        toggleIsResizeActive(false);

        let width = Math.round(size.width);

        setResizableColumns((prev) => {
          const temp = [...prev];

          const previousWidth = Number(prev[index].width);

          if (index === columns.length - 1) {
            const delta = width - previousWidth;
            const adjustedDelta = delta * getMultiplier(delta);
            width = previousWidth + adjustedDelta;
            width = Math.max(width, constants.MIN_COLUMN_WIDTH);
          }

          temp[index] = { ...prev[index], width };

          return temp;
        });

        if (resizableProps?.tableSizes && resizableProps?.onResizeSave) {
          const tempSizes = { ...resizableProps.tableSizes };

          tempSizes[columns[index].title as string] = width;

          resizableProps.onResizeSave(tempSizes);
        }
      },
    [columns, resizableProps],
  );

  const onHeaderCell = useCallback<OnHeaderCellCallback>(
    (index) => (columnCell) => ({
      /** @remark Не видит свойство width */
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      width: columnCell.width,
      onResize: onResize(index),
      onMouseDown,
    }),
    [onMouseDown, onResize],
  );

  useLayoutEffect(() => {
    if (resizableProps?.isActive) {
      if (
        headerRef &&
        (headerRef as MutableRefObject<HTMLDivElement>) === null
      ) {
        return;
      }

      const header = (headerRef as MutableRefObject<HTMLDivElement>).current;

      const { left } = header.getBoundingClientRect();

      setTableContainerPositionLeft(left ?? 0);
    }
  }, [headerRef, columns, resizableProps?.isActive]);

  useEffect(() => {
    if (resizableProps?.tableSizes) {
      setResizableColumns(
        columns.map((column) => ({
          ...column,
          width:
            resizableProps?.tableSizes &&
            resizableProps?.tableSizes[column.title as string]
              ? resizableProps?.tableSizes[column.title as string]
                ? resizableProps.tableSizes[column.title as string]
                : 100
              : column.width
              ? column.width
              : 100,
        })),
      );
    } else {
      setResizableColumns(columns);
    }
  }, [columns]); // eslint-disable-line

  return [
    resizableColumns,
    onHeaderCell,
    isResizeActive,
    onResizeMove,
    mousePosition - tableContainerPositionLeft,
  ];
};
