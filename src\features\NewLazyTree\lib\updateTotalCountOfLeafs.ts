import { TreeState } from '../types';

export const updateTotalCountOfLeafs = (
  nodeId: string | undefined,
  tree: TreeState,
): void => {
  const node = nodeId && tree.entities[nodeId];
  if (!node) return;

  node.totalCountOfLeafs = node?.childrenIds?.reduce((total, childId) => {
    const child = tree.entities[childId];
    if (!child || child.isSkeleton) {
      return total + (child?.totalCountOfLeafs || 1);
    }
    return (
      total +
      (child.isDirectory || child.isTable ? child.totalCountOfLeafs || 0 : 1)
    );
  }, 0);

  updateTotalCountOfLeafs(node.parent, tree);
};
