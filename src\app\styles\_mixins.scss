@keyframes fade {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.3;
  }
}

@mixin fullSizePopup {
  height: 95vh;
  width: 95vw;
}

@mixin scrollBar {
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 7px;
    height: 7px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgb(80 80 80 / 50%);
    border-radius: 2px;
  }
}

@mixin fadeAnimation {
  animation: fade 2s linear infinite;
}

@mixin defaultPopupStyles {
  margin: auto;
  padding: 20px;
  border-radius: 4px;
}

@mixin defaultBorder {
  border: 1px solid #d9d9d9;
}

@mixin customTreeTitleWidth {
  :global(.ant-tree-treenode) {
    width: 100% !important;
  }

  :global(.ant-tree-node-content-wrapper) {
    width: 100% !important;
  }
}

@mixin virtualScrollbar {
  :global(.ant-tree-list-scrollbar) {
    visibility: visible !important;
    background-color: rgb(241 241 241) !important;
  }

  :global(.ant-tree-list-scrollbar-thumb) {
    visibility: visible !important;
    width: 7px !important;
    background-color: rgb(193 193 193) !important;
    border-radius: 2px !important;

    &::before,
    &::after {
      position: absolute !important;
      left: 0 !important;
      content: '' !important;
      width: 100% !important;
      height: 8px !important;
      background-color: rgb(241 241 241) !important;
    }

    &::before {
      top: 0 !important;
    }

    &::after {
      bottom: 0 !important;
    }
  }

  :global(.ant-tree-list-scrollbar-vertical) {
    &::before,
    &::after {
      position: absolute !important;
      left: 50% !important;
      content: '' !important;
      transform: translateX(-50%) !important;
      width: 6px !important;
      height: 6px !important;
      border-left: 3px solid transparent !important;
      border-right: 3px solid transparent !important;
    }

    &::before {
      top: 0 !important;
      border-bottom: 3px solid rgb(0 0 0 / 50%) !important;
      z-index: 3;
    }

    &::after {
      bottom: 0 !important;
      border-top: 3px solid rgb(0 0 0 / 50%) !important;
    }
  }
}
