import {
  FileOutlined,
  PaperClipOutlined,
  SendOutlined,
} from '@ant-design/icons';
import { Button, DatePicker, Input, Space, Tooltip } from 'antd';
import axios from 'axios';
import classNames from 'classnames';
import moment from 'moment';
import { ReactElement, useMemo } from 'react';
import type { FC } from 'react';
import { Link } from 'react-router-dom';
import { useToggle } from 'react-use';

import {
  FilenetInfoCardProps,
  PrivateFilenetInfoCardProps,
} from 'features/FilenetInfoCards/types';
import { FilenetActions } from 'entities/FilenetActions';
import {
  FilenetAttachmentsTree,
  ParsedDossierStructureTree,
} from 'entities/FilenetAttachmentsTree';
import { FilenetOrganizeObjects } from 'entities/FilenetOrganizeObjects';

import { apiUrls } from 'shared/api';
import { errorMessages } from 'shared/config';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import { AppPopup, ApiContainer } from 'shared/ui';
import { InputRow } from 'shared/ui/InputRow';

import { hooks } from '../../store';

import styles from './styles.module.scss';

const renderTitle = (node: ParsedDossierStructureTree): ReactElement => (
  <Space>
    <FileOutlined /> &nbsp;
    <span>
      <span className={styles.title}>{node.title}</span>
      <FilenetActions
        permissions={node.viewDownloadPermissions ?? []}
        cardId={node.id ?? 0}
        attachment={(() => {
          const paths = String(node.title).split('/');

          return {
            id: String(node.attachmentId || 0),
            filename: String(paths.at(-1)),
          };
        })()}
        hideNotEnoughRights
        size="small"
      />
    </span>
  </Space>
);

const Card: FC<PrivateFilenetInfoCardProps> = ({
  popupId,
  handleDossierOpen,
  isFullSize,
}) => {
  const [rows, { data, error, isPending, isResEnd }] =
    hooks.useGetFilenetCardRows(popupId);

  const inputs = useMemo(() => {
    if (isResEnd && data) {
      const list = [
        { label: 'ID файла', value: data.id || '' },
        { label: 'Наименование файла', value: data.title || '' },
        { label: 'Полный путь', value: data.contentPath || '' },
        { label: 'Изменен', value: data.modifiedDatetime || '' },
        {
          label: 'Загрузка ИВС ГИБР',
          value: data.uploadGibrDatetime || '',
        },
        {
          label: 'Загрузка в КЗ ИД',
          value: data.publishedDatetime || '',
        },
        {
          label: 'Размер',
          value: data.fileSize || '',
        },
        { label: 'Тип файла', value: data.documentTypeName || '' },
      ].map(({ label, value }) => (
        <InputRow title={label} key={label}>
          <Input
            disabled
            value={value}
            className={styles.input}
            size={isFullSize ? 'middle' : 'small'}
          />
        </InputRow>
      ));

      list.push(
        <InputRow title="Период проверки" key="range-picker">
          <DatePicker.RangePicker
            disabled
            format={DEFAULT_DATE_VARIANT}
            size={isFullSize ? 'middle' : 'small'}
            className={styles.input}
            value={[
              moment(data.auditPeriodFrom, DEFAULT_DATE_VARIANT),
              moment(data.auditPeriodTo, DEFAULT_DATE_VARIANT),
            ]}
            inputReadOnly
          />
        </InputRow>,
      );

      list.push(
        <InputRow title="Досье проверки" key="dossier-finder">
          <div className={classNames(styles.row, styles.rowSpace)}>
            {data.auditCardName && data.auditCardId && (
              <Link
                to={`/${apiUrls.fileNet.dossierCard(data.auditCardId)}`}
                target="_blank"
              >
                <SendOutlined /> &nbsp; {data.auditCardName}
              </Link>
            )}

            <Button
              type="primary"
              onClick={() => handleDossierOpen(String(data.auditCardId))}
              size={isFullSize ? 'middle' : 'small'}
            >
              Структура досье проверки
            </Button>
          </div>
        </InputRow>,
      );

      list.push(
        <InputRow title="Действия" key="actions">
          <FilenetActions
            permissions={data.viewDownloadPermissions || []}
            cardId={data.id as number}
            attachment={(() => {
              const files = data.title.split('/');

              return {
                id: String(data.attachmentId),
                filename: String(files.at(-1)),
              };
            })()}
          />
        </InputRow>,
      );

      list.push(
        <InputRow title="Приложения" key="hidden-popup">
          {data?.folder && data?.id ? (
            <FilenetAttachmentsTree
              renderTitle={renderTitle}
              folder={data.folder}
              id={data.id}
            />
          ) : (
            <Tooltip placement="left" title="Приложения отсутствуют">
              <Button disabled icon={<PaperClipOutlined />} type="primary" />
            </Tooltip>
          )}
        </InputRow>,
      );

      return list;
    }

    return null;
  }, [isResEnd, data, isFullSize]); // eslint-disable-line

  return (
    <ApiContainer
      error={error}
      isPending={isPending}
      errorTitle={
        axios.isAxiosError(error)
          ? error.response?.data.message
          : errorMessages.pendingError
      }
      errorStatus={404}
    >
      <div className={classNames(styles.column)}>
        {inputs}

        <FilenetOrganizeObjects isFullSize={isFullSize} rows={rows} />
      </div>
    </ApiContainer>
  );
};

export const FileCard: FC<FilenetInfoCardProps> = ({
  onClose,
  popupId,
  handleDossierOpen,
  isOpened,
}) => {
  const [isFullSize, toggleFullSize] = useToggle(false);

  return (
    <AppPopup
      isOpened={isOpened}
      onClose={onClose}
      key="file-card"
      title="Карточка файла"
      fullSizeClassName={styles.container_fullSize}
      additionalFullSizeHandler={toggleFullSize}
      className={styles.container}
    >
      <Card
        handleDossierOpen={handleDossierOpen}
        popupId={popupId}
        isFullSize={isFullSize}
      />
    </AppPopup>
  );
};
