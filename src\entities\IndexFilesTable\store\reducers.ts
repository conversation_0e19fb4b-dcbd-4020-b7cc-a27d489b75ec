import { createSlice } from '@reduxjs/toolkit';
import { indexFilesColumns } from '../config';
import type { IndexFilesInitial } from '../types';
import { getIndexFilesThunk } from './thunks';

const initialState: IndexFilesInitial = {
  isPending: false,
  error: null,
  table: {
    columns: [],
    rows: [],
    pagination: { total: 0, pageSize: 20 },
    pageNumber: 1,
  },
};

export const slice = createSlice({
  name: 'indexFiles',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getIndexFilesThunk.pending, (state) => {
      state.isPending = true;
      state.error = null;
    });

    builder.addCase(getIndexFilesThunk.fulfilled, (state, { payload }) => {
      state.isPending = false;
      state.table.columns = indexFilesColumns;
      state.table.rows = payload.list[0].children.reduce((acc, item) => {
        acc.push({
          ...item.data,
          key: item.data.id,
          name: `${item.data.name} (${item.data.description})`,
        });

        return acc;
      }, [] as import('features/DataGrid').TableRowData[]);
    });

    builder.addCase(getIndexFilesThunk.rejected, (state, { error }) => {
      state.isPending = false;
      state.error = error;
    });
  },
});
