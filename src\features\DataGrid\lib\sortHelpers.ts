/**
 * Сравнивает две строки с использованием правил русского языка, включая
 * числовую сортировку и базовую чувствительность к регистру.
 *
 * @param {string} a - Первая строка для сравнения.
 * @param {string} b - Вторая строка для сравнения.
 * @returns {number} - Отрицательное число, если 'a' предшествует 'b';
 *   положительное число, если 'a' следует за 'b'; ноль, если они эквивалентны.
 */
const intlCollator = new Intl.Collator('ru', {
  numeric: true,
  sensitivity: 'base',
}).compare;

export const compareStrings = intlCollator;

/**
 * Проверяет, может ли данная строка быть преобразована в допустимое число.
 *
 * @param {string} value - Строка для проверки.
 * @returns {boolean} - True, если строка представляет допустимое число; иначе
 *   False.
 */
export const isNumber = (value: string): boolean =>
  !Number.isNaN(Number(value));

/**
 * Сравнивает две числовые строки, преобразуя их в числа с плавающей точкой.
 *
 * @param {string} a - Первая числовая строка для сравнения.
 * @param {string} b - Вторая числовая строка для сравнения.
 * @returns {number} - Разница между преобразованными числами.
 */
export const compareNumbers = (a: string, b: string): number =>
  parseFloat(a) - parseFloat(b);

/**
 * Проверяет, является ли значение null, undefined, пустой строкой или строкой
 * 'null'.
 *
 * @param {unknown} value - Значение для проверки.
 * @returns {boolean} - True, если значение null, undefined, пустая строка или
 *   'null'; иначе False.
 */
export const isEmptyOrNull = (value: unknown): boolean =>
  value === null ||
  value === undefined ||
  (typeof value === 'string' && value.trim() === '') ||
  value === 'null' ||
  value === '-';

const dateRegex =
  // eslint-disable-next-line max-len
  /^(0[1-9]|[12][0-9]|3[01])[.-](0[1-9]|1[0-2])[.-](\d{4})(?:\s(\d{2}):(\d{2})(?::(\d{2}))?)?$|^(0[1-9]|1[0-2])[.-](\d{4})$/;

/**
 * Преобразует разобранный массив даты в метку времени.
 *
 * @param {RegExpMatchArray} parsedDate - Результат сопоставления строки даты с
 *   dateRegex.
 * @returns {number} - Метка времени, соответствующая разобранной дате.
 */
function getTimestamp(parsedDate: RegExpMatchArray): number {
  if (parsedDate[7] && parsedDate[8]) {
    // Формат ММ.ГГГГ
    const date = new Date(
      Number(parsedDate[8]),
      Number(parsedDate[7]) - 1,
      1, // Устанавливаем день как первый день месяца
    );
    return date.getTime();
  }
  // Формат ДД.ММ.ГГГГ
  const date = new Date(
    Number(parsedDate[3]),
    Number(parsedDate[2]) - 1,
    Number(parsedDate[1]),
    Number(parsedDate[4]) || 0,
    Number(parsedDate[5]) || 0,
    Number(parsedDate[6]) || 0,
  );
  return date.getTime();
}

/**
 * Сравнивает две строки дат на основе их меток времени. Поддерживаемые форматы
 * дат:
 *
 * 1. ДД.ММ.ГГГГ
 * 2. ДД-ММ-ГГГГ
 * 3. ММ.ГГГГ
 * 4. ММ-ГГГГ
 * 5. ДД.ММ.ГГГГ чч:мм
 * 6. ДД-ММ-ГГГГ чч:мм
 * 7. ДД.ММ.ГГГГ чч:мм:сс
 * 8. ДД-ММ-ГГГГ чч:мм:сс
 *
 * @param {string} firstDate - Первая строка даты для сравнения.
 * @param {string} secondDate - Вторая строка даты для сравнения.
 * @returns {number} - Разница между метками времени дат. Если даты недопустимы,
 *   сравнивает их лексикографически.
 */
export function compareDates(firstDate: string, secondDate: string): number {
  const firstMatch = firstDate.match(dateRegex);
  const secondMatch = secondDate.match(dateRegex);

  if (firstMatch && secondMatch) {
    const firstTime = getTimestamp(firstMatch);
    const secondTime = getTimestamp(secondMatch);

    return firstTime - secondTime;
  }

  return firstDate.localeCompare(secondDate);
}

/**
 * Проверяет, является ли данная строка допустимой датой в поддерживаемых
 * форматах. Поддерживаемые форматы дат:
 *
 * 1. ДД.ММ.ГГГГ
 * 2. ДД-ММ-ГГГГ
 * 3. ММ.ГГГГ
 * 4. ММ-ГГГГ
 * 5. ДД.ММ.ГГГГ чч:мм
 * 6. ДД-ММ-ГГГГ чч:мм
 * 7. ДД.ММ.ГГГГ чч:мм:сс
 * 8. ДД-ММ-ГГГГ чч:мм:сс
 *
 * @param {string} dateValue - Строка даты для проверки.
 * @returns {boolean} - True, если строка даты соответствует одному из
 *   поддерживаемых форматов; иначе False.
 */
export function isValidDate(dateValue: string): boolean {
  return dateRegex.test(dateValue);
}
