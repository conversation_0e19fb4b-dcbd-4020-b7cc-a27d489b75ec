import { FC } from 'react';
import { ExportProps } from 'features/DownloadModal/types';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

export const Export: FC<ExportProps> = ({ onClick }) => (
  <ButtonsContainer
    buttons={[
      {
        title: 'Экспорт в XLSX',
        key: 'XLSX',
        id: 'df8272a6-bc71-425f-aa17-cc1924f1a3d6',
        tooltip:
          'Выполнить поиск в базе данных по заданным условиям и вывести в файл формата XLSX',
        tooltipProps: {
          placement: 'top',
        },
        type: 'primary',
        onClick,
      },
    ]}
  />
);
