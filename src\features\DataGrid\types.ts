import { SerializedError } from '@reduxjs/toolkit';
import { InputRef } from 'antd';
import {
  ColumnFilterItem,
  ColumnType,
  FilterDropdownProps,
} from 'antd/lib/table/interface';
import type { LegacyRef, ReactElement, ReactNode, Ref } from 'react';

export interface TableColumnData extends ColumnType<TableRowData> {
  /** Индекс даты для привязки к контексту */
  dataIndex: string;
  /** Выравнивание колонки */
  align?: 'left' | 'right' | 'center';
  /** Передается стринга с названием компонента для проверки и рендера в ряды */
  columnType?: string;
  /** Айди колонки, нужен для сортировки */
  columnUuid?: string;
  /** Проп эллипсиса колонок */
  ellipsis?:
    | {
        showTitle?: boolean;
      }
    | boolean;
  filterType?: 'default' | 'query' | 'date' | 'numRange' | null;
  /** Фильтрация в конкретной колонке */
  filters?: ColumnFilterItem[];
  /** Опциональный ключ для скрытия поиска */
  hideColumnSearch?: boolean;
  /** Проп на скрытие сортировки по колонке */
  hideSorter?: boolean;
  /** Проп для кастомного сортинга */
  lazySortOrder?: SortOrder;
  /** Коллбэк для фильтрации в конкретной колонке */
  onFilter?: (value: unknown, record: TableRowData) => boolean;
  /** Колбэк на лези сортировку */
  onLazyLoadSort?: (newSortOrder: TableColumnData['lazySortOrder']) => void;
  /** Проп на кастомный рендер компонента */
  render?: (text: string, row: TableRowData) => ReactElement;
  /** Проп на кастомный рендер титула */
  renderTitle?: (title: Title) => ReactElement;
  /** Проп на отображение кастомной сортировки */
  sortable?: boolean;
  /** Ширина колонки */
  width?: string | number;
}

export type TableRowData = {
  /** Ключ */
  key: Key;
  /** Простой статус чекбокса */
  checkbox?: boolean;
  /** Объект со статусами для филтрации по чекбоксу */
  checkboxStatus?: Record<string, boolean>;
  /** Потомки */
  children?: TableRowData[];
  /** Массив данных для нестед компонента */
  expandedData?: unknown[];
  /** Нестед таблица */
  nestedTable?: TableRowData[] | TableColumnsAndRows | NestedTabsWithTable;
  /** Статус ленивой загрузки nestedTable */
  nestedTableStatus?: {
    error: SerializedError | null;
    isError: boolean;
    isLoaded: boolean;
    isLoading: boolean;
  };
  /** Объект ключей для запроса */
  rowId?: Record<string, string>;
  /** Заголовок таба */
  tabTitle?: string;
  /**
   * Ключи и значения должны быть строкой, но Максим начал пулять лишнюю инфу,
   * добавил null для проверки
   */

  // В ключе может быть абсолютно любое значение
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
} & Record<string, any>;

/** Объект со значениями ширины */
export type TableSizes = Record<string, string | number>;

export type DataGridProps = {
  /** Массив с дополнительными кнопками */
  additionalButtons?: AdditionalButton[];
  /** Дополнительные класснеймы */
  additionalClassNames?: {
    /** Для контейнера кнопок */
    buttonsContainer?: string;
    /** Для контейнера */
    container?: string;
    /** Для курсора */
    cursor?: string;
    /** Для футера */
    footer?: string;
    /** Для спинера */
    spinner?: string;
    /** Для таблицы */
    table?: string;
  };
  /** Дополнительный компонент */
  additionalComponent?: ReactNode | null;
  /** Пропсы для колонок */
  columnsProps?:
    | ((column: TableColumnData) => Partial<TableColumnData>)
    | Partial<TableColumnData>;
  /** Ref ссылки на элементы */
  elementsRef?: {
    header?: LegacyRef<HTMLDivElement>;
    table?: Ref<HTMLDivElement>;
  };

  /** Объект с массивом чекбоксов фильтрации и опциональным заголовком */
  filterCheckbox?: {
    checkboxes: TableAdditionalCheckbox[];
    title?: Title;
  };
  /** Нода для проброса в футер таблицы */
  footerAdditionalComponent?: ReactNode;
  /** Проп на скрытие кнопок поиска по колонке */
  hideColumnSearch?: boolean;
  /** Проп на скрытие сортировки по колонке */
  hideSorter?: boolean;
  /** Состояние загрузки */
  isLoading?: boolean;

  /** Опциональный асинхронный загрузчик вложенной таблицы для row */
  nestedTableLoader?: (row: TableRowData) => void;

  /** Пропсы нестед таблицы */
  nestedTableProps?:
    | ((row: TableRowData) => Omit<DataGridProps, 'columns' | 'rows'>)
    | Omit<DataGridProps, 'columns' | 'rows'>;

  /** Спред для проброса пропсов пагинации и доп. пропсов */
  paginationProps?: import('antd/lib/pagination/Pagination').PaginationProps & {
    /** Текущая страница */
    currentPage: number;
    /** Ключ для показа пагинации */
    show: boolean;
    /** Тотал страниц */
    total: number;
    /** Скрыть ли всего записей */
    hideTotalLabel?: boolean;
    /** Обработчик клика по дропдауну количества элементов */
    onItemsOnPageClick?: (page: number, elements: number) => void;
    /** Обработчик клика по пагинации */
    onPaginatorClick?: (page: number, pageSize: number) => void;
  };

  /** Опциональный проп для включения возможности ресайза колонок */
  resizableProps?: {
    /** Пропс на включение ресайза */
    isActive?: boolean;
    /** Колбэк на сохранение массива куда либо */
    onResizeSave?: (data: TableSizes) => void;
    /** Массив значений уже сохраненных фильтров */
    tableSizes?: TableSizes;
  };

  /** Проброс колбэка для rowSelection */
  rowSelectionProps?: { selectAll: (rows: TableRowData[]) => void };

  /** Пропсы для компонента поиска */
  searchProps?: {
    /** Ключ для показа инпута с поиском */
    show: boolean;
    /** Колбек очистки выделенных строк */
    clearSelection?: () => void;
    /** Лейбл пагинации */
    label?: string;
    /** Колбек со значением инпута */
    onInput?: (inputValue: string) => void;
  };

  /** Массив с дополнительными кнопками на второй ряд */
  secondRowAdditionalButtons?: AdditionalButton[];

  /** Опциональный колбэк для получения фильтрованных rows */
  setFilteredRows?: (rows: TableRowData[]) => void;

  /** Отображать ли чекбокс "Оторажать только найденные в форме поиска" */
  showOnlyFounded?: boolean;

  /** Дополнительные обработчики */
  tableAdditionHandlers?: {
    /** Обработчик клика по ряду */
    onRowClick: (data: TableRowData, isDoubleClick: boolean) => void;
  };

  /** Спред для проброса пропсов таблицы анта */
  tableAdditionProps?: Omit<
    import('antd/lib/table/Table').TableProps<TableRowData>,
    'columns'
  >;
} /** Обязательные пропы колонки и ряды */ & TableColumnsAndRows;

/** Интерфейс для дополнительного фильтрационного чекбокса */
export interface TableAdditionalCheckbox {
  /** Лейбл чекбокса */
  label: string;
  /** Значение чекбокса */
  value: string;
  /** Чек статус */
  checked?: boolean;
  /** Состояние disabled */
  disabled?: boolean;
  /** Тултип по чекбоксу */
  hint?: string;
}

export interface ColumnFilters {
  text: string;
  value: unknown;
}

export interface NestedTabsWithTable {
  tabs: TabsWithTable[];
}

interface TabsWithTable {
  endpoint: string;
  key: Key;
  label: string;
  tableData: TableColumnsAndRows;
}

/** Пропы компонента заголовка колонки с лези лоад загрузкой */
export interface TitleWithLazySortProps {
  /** Текущее состояние сортировки */
  currentSort: TableColumnData['lazySortOrder'];
  /** Колбек на изменение порядка сортировки */
  onSort: (newSortOrder: TableColumnData['lazySortOrder']) => void;
  /** Заголовок колонки */
  title: TableColumnData['title'];
}

/** Пропы компонента фильтров */
export type AppFilterDropDownProps = Pick<
  FilterDropdownProps,
  'selectedKeys' | 'setSelectedKeys' | 'confirm' | 'clearFilters'
> & {
  /** Реф инпута */
  searchInput: Ref<InputRef>;
  /** Заголовок колонки */
  title?: TableColumnData['title'];
};

/** Выгрузка Required значения onRowClick из пропсов таблицы */
export type PickOnRowClick = NonNullable<
  DataGridProps['tableAdditionHandlers']
>['onRowClick'];

/** Конфиг пресета таблиц */
export interface TableConfig {
  asColumns: TableColumnData[];
  asRows: TableRowData[];
  sortValue: SortValue;
}

export type SortValue = {
  columnUuid: string;
  value: SortOrder;
} | null;
