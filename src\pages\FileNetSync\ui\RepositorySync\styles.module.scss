.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 10px;

  &Checkbox {
    margin: 0 !important;
    display: flex !important;
    align-items: center;

    &Text {
      display: flex;
      align-items: center;
      gap: 5px;
      margin: 0;
    }
  }
}

.buttons {
  display: flex;
  gap: 20px;
  width: 100%;
  flex-wrap: wrap;
}

@media screen and (max-width: 1500px) {
  .popup {
    width: 60%;
  }
}

.title {
  text-align: center;
}
