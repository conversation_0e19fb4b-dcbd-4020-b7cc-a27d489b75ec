import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  LinkedData,
  WorkGroupEPCConfig,
  WorkGroupEPCInitial,
} from 'widgets/WorkGroupEPC';
import { treeParserByConfig } from 'shared/lib';
import {
  getEpcTreeThunk,
  postDeleteFiles,
  postSearchThunk,
  getDownloaderLink,
} from './actions';

export const initialState: WorkGroupEPCInitial = {
  epc: {
    buttons: { isPending: false, error: null },
    treeData: { tree: [], isPending: false, error: null },
    linkedData: { directoryId: '', watchLinked: [], editLinked: [], title: '' },
  },
};

export const slice = createSlice({
  name: 'workGroupEpc',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
    handleUpdateLinked: (state, { payload }: PayloadAction<LinkedData>) => {
      state.epc.linkedData = payload;
      state.epc.treeData.tree = treeParserByConfig(
        state.epc.treeData.tree,
        (node) => {
          if (payload?.directoryId === node?.itemId) {
            if (payload.watchLinked.length > 0) {
              node.hasLinked = true;
            } else {
              node.hasLinked = false;
            }
          }
          return node;
        },
      );
    },
    handleResetLinked: (state) => {
      state.epc.linkedData.editLinked = state.epc.linkedData.watchLinked;
    },
    handleTree: (state, { payload }: PayloadAction<TreeElement[]>) => {
      state.epc.treeData.tree = payload;
    },
    setIsPendingTree: (state, { payload }) => {
      state.epc.treeData.isPending = payload;
    },
    handleResetSearchTags: (state) => {
      state.epc.treeData.isPending = true;
      state.epc.treeData.tree = treeParserByConfig(
        state.epc.treeData.tree,
        (node) => {
          if (node.tagged) {
            node.tagged = false;
          }
          return node;
        },
      );
      state.epc.treeData.isPending = false;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getEpcTreeThunk.pending, (state) => {
      state.epc.treeData.isPending = true;
      state.epc.treeData.error = null;
    });

    builder.addCase(getEpcTreeThunk.rejected, (state, { error }) => {
      state.epc.treeData.isPending = false;
      state.epc.treeData.error = error;
    });

    builder.addCase(getEpcTreeThunk.fulfilled, (state, { payload }) => {
      state.epc.treeData.isPending = false;
      state.epc.treeData.tree = treeParserByConfig(
        payload.treeData,
        (node) =>
          ({
            ...node,
            disabled: false,
            isDisabled: node.disabled,
          } as TreeElement),
      );
    });
    builder.addCase(postSearchThunk.fulfilled, (state, { payload }) => {
      if (
        payload.treeData.length === 1 &&
        payload.treeData[0].children?.length === 0
      ) {
        return;
      }
      state.epc.treeData.tree = treeParserByConfig(
        payload.treeData,
        WorkGroupEPCConfig.treeParserConfig,
      );
    });

    builder.addCase(postDeleteFiles.pending, (state) => {
      state.epc.buttons.isPending = true;
      state.epc.buttons.error = null;
    });

    builder.addCase(postDeleteFiles.rejected, (state, { error }) => {
      state.epc.buttons.isPending = false;
      state.epc.buttons.error = error;
    });

    builder.addCase(postDeleteFiles.fulfilled, (state) => {
      state.epc.buttons.isPending = false;
    });

    builder.addCase(getDownloaderLink.pending, (state) => {
      state.epc.buttons.isPending = true;
      state.epc.buttons.error = null;
    });

    builder.addCase(getDownloaderLink.rejected, (state, { error }) => {
      state.epc.buttons.isPending = false;
      state.epc.buttons.error = error;
    });

    builder.addCase(getDownloaderLink.fulfilled, (state) => {
      state.epc.buttons.isPending = false;
    });
  },
});
