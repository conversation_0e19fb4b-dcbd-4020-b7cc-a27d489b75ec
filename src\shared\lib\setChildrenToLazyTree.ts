const setKeysToNodes = (nodes: TreeElement[], key: Key, startIndex: number = 0): TreeElement[] => {
  nodes.map((elem: TreeElement, i) => {
    elem.key = `${key}-${startIndex + i}`;
    return elem;
  });
  return nodes;
};

export const setChildrenToLazyTree = (
  list: TreeElement[],
  key: Key,
  children: TreeElement[],
  appendMode: boolean = false,
): TreeElement[] =>
  list.map((node) => {
    if (node.key === key) {
      if (appendMode && node.children) {
        // В режиме добавления объединяем существующие дочерние элементы с новыми
        const existingChildren = node.children;
        const newChildren = setKeysToNodes(children, key, existingChildren.length);
        const combinedChildren = [...existingChildren, ...newChildren];

        return {
          ...node,
          children: combinedChildren,
          childrenCount: combinedChildren.length,
          // Обновляем счетчик загруженных элементов
          loadedItemsCount: combinedChildren.length,
        };
      } else {
        // Обычный режим - заменяем дочерние элементы
        return {
          ...node,
          children: setKeysToNodes(children, key),
          childrenCount: children.length,
          loadedItemsCount: children.length,
        };
      }
    }

    if (node.children) {
      return {
        ...node,
        children: setChildrenToLazyTree(node.children, key, children, appendMode),
      };
    }

    return node;
  });
