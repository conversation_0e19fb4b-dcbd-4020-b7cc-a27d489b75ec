import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tag } from 'antd';
import type { FC } from 'react';
import styles from './styles.module.scss';

interface RenderQuestionsProps {
  questions: string[];
  systemHint: string;
}

export const RenderAdditionalTags: FC<RenderQuestionsProps> = ({
  questions,
  systemHint,
}) => (
  <div className={styles.additionalTags}>
    {systemHint && <Tag color="success">{systemHint}</Tag>}
    {questions.length > 0 && (
      <>
        <span className={styles.questionsHeader}>Коды вопросов:</span>
        <div className={styles.questions}>
          {[...questions]
            .sort((a, b) => a.localeCompare(b))
            .map((item) => (
              <Tag
                key={item}
                color="processing"
                icon={<QuestionCircleOutlined />}
              >
                {item}
              </Tag>
            ))}
        </div>
      </>
    )}
  </div>
);
