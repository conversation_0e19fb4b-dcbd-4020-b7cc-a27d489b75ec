import axios, { AxiosResponse } from 'axios';
import { useCallback } from 'react';
import { v4 } from 'uuid';
import { Endpoints } from 'features/NewLazyTree/types';
import { apiUrls } from 'shared/api';
import { useAppDispatch } from 'shared/model';
import {
  actionOnCheckedNodesThunk,
  deleteNodeThunk,
  moveNodesThunk,
} from '../thunks';

// Хук для операций с узлами
export const useTreeActions = ({
  treeKey,
  endpoints,
  queryParams,
  transformResponse,
  controllersRef,
  getAbortController,
}: {
  controllersRef: React.MutableRefObject<Map<string, AbortController>>;
  endpoints: Endpoints;
  getAbortController: (key: string) => AbortController;
  queryParams: Record<string, unknown>;
  treeKey: string;
  transformResponse?: (response: AxiosResponse) => TreeElement[];
}): typeof treeActions => {
  const dispatch = useAppDispatch();

  const deleteNode = useCallback(
    async ({
      itemId,
      fileNetId,
      parentId,
    }: {
      fileNetId: string;
      itemId: string;
      parentId: string;
    }): Promise<string> =>
      new Promise((resolve, reject) => {
        const controller = getAbortController(itemId);
        dispatch(
          deleteNodeThunk({
            endpoints: {
              delete: endpoints.delete,
              refetch: apiUrls.workGroup.EPC.getLazyTree,
            },
            itemId,
            fileNetId,
            parentId,
            treeKey,
            queryParams,
            signal: controller.signal,
            transformResponse,
          }),
        )
          .unwrap()
          .then((value) => {
            controllersRef.current.delete(itemId);
            resolve(value);
          })
          .catch((error) => {
            controllersRef.current.delete(itemId);
            reject(error);
          });
      }),
    [
      controllersRef,
      dispatch,
      endpoints.delete,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const moveNode = useCallback(
    async (body: MoveDNDBody, node: TreeElement) => {
      try {
        const controller = getAbortController(node.itemId!);
        const res = await dispatch(
          moveNodesThunk({
            endpoint: {
              move: endpoints.move,
              refetch: endpoints.fetch,
            },
            nodes: [node],
            treeKey,
            body,
            transformResponse,
            queryParams,
            signal: controller.signal,
          }),
        ).unwrap();
        controllersRef.current.delete(node.itemId!);
        return res;
      } catch (err) {
        controllersRef.current.delete(node.itemId!);
        if (axios.isAxiosError(err)) {
          if (err.response && err.response.data && err.response.data.error) {
            return err.response.data.error;
          }
        }
        return 'Произошла ошибка перемещения файла';
      }
    },
    [
      controllersRef,
      dispatch,
      endpoints.fetch,
      endpoints.move,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const actionOnCheckedNodes = useCallback(
    async ({
      actionCallback,
      updateCallback,
      filterCallback,
      successMessage,
    }: {
      actionCallback: (nodes: TreeElement[]) => Promise<void>;
      filterCallback: (nodes: TreeElement) => boolean;
      successMessage: string;
      updateCallback?: (filteredNodes: TreeElement) => void;
    }) => {
      const abortId = v4();
      try {
        const controller = getAbortController(abortId);
        const res = await dispatch(
          actionOnCheckedNodesThunk({
            actionCallback,
            updateCallback,
            filterCallback,
            treeKey,
            transformResponse,
            queryParams,
            endpoint: endpoints.fetch,
            signal: controller.signal,
            successMessage,
          }),
        ).unwrap();

        return res;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          return err.message;
        }
        return String(err);
      }
    },
    [
      dispatch,
      endpoints.fetch,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const treeActions = {
    deleteNode,
    moveNode,
    actionOnCheckedNodes,
  };

  return treeActions;
};
