import { createContext, FC, useMemo } from 'react';
import { v4 } from 'uuid';

export const KeyContext = createContext<string | null>(null);

type TreeKeyProviderProps = {
  children: React.ReactNode;
};

export const TreeKeyProvider: FC<TreeKeyProviderProps> = ({ children }) => {
  const treeKey = useMemo(() => `tree_${v4()}`, []);
  return <KeyContext.Provider value={treeKey}>{children}</KeyContext.Provider>;
};
