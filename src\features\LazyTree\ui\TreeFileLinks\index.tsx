import { FileTextOutlined, FileDoneOutlined } from '@ant-design/icons';
import { Space, Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import { FC } from 'react';
import { apiUrls } from 'shared/api';
import { ButtonsContainer } from 'shared/ui';
import { TreeFileLinksProps } from '../..';

import { renderTitle } from '../renderTitle';
import styles from './styles.module.scss';

export const TreeFileLinks: FC<TreeFileLinksProps> = ({
  node,
  getViewerLink,
}) => {
  const stringTitle = node.title as string;

  const tooltipTitle = stringTitle.replace(/\s\(.*/, '');

  const title = renderTitle(node);
  const canViewFile = 'fileNetId' in node && Boolean(node?.canView);

  if (node?.isDirectory) {
    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <>{title}</>;
  }

  return (
    <Space>
      <Tooltip
        title={`Открыть файл "${tooltipTitle}" в визуализаторе`}
        placement="right"
        trigger={canViewFile ? ['hover'] : []}
      >
        <Typography.Text
          className={classNames(
            styles.button,
            canViewFile && styles.buttonAllowed,
          )}
          onClick={() => {
            if (canViewFile && node.fileNetId && node.cardId) {
              getViewerLink(node.fileNetId, node.title as string, node.cardId);
            }
          }}
        >
          {title}
        </Typography.Text>
      </Tooltip>

      <ButtonsContainer
        buttons={(() => {
          const buttons = [];

          if (node.cardId) {
            buttons.push({
              disabled: !canViewFile,
              key: 'fileCard',
              title: '',
              icon: <FileTextOutlined />,
              tooltip: 'Переход к карточке файла',
              onClick: () =>
                window.open(apiUrls.fileNet.fileCard(node.cardId!)),
            });
          }

          if (node.dossierId) {
            buttons.push({
              disabled: !canViewFile,
              key: 'dossierId',
              title: '',
              icon: <FileDoneOutlined />,
              tooltip: 'Переход к досье проверки',
              onClick: () =>
                window.open(apiUrls.fileNet.dossierCard(node.dossierId!)),
            });
          }

          return buttons;
        })()}
      />
    </Space>
  );
};
