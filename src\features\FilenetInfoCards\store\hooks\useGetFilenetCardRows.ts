import { useState } from 'react';
import { useEffectOnce } from 'react-use';
import type { FilenetResponseData } from 'entities/FilenetAttachmentsTree';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { useAxiosRequest } from 'shared/model';

type Rows = [TableColumnsAndRows['rows'], TableColumnsAndRows['rows']];
export const useGetFilenetCardRows = (
  popupId: string | number,
): [Rows, typeof statuses] => {
  const [trigger, statuses] = useAxiosRequest<FilenetResponseData>(
    filenetServiceInstance,
  );
  const [rows, setRows] = useState<Rows>([[], []]);

  const fetchData: PromiseCallback = async () => {
    const data = await trigger(apiUrls.fileNet.filenetCardInfo(popupId));

    const inspectorRows = data.inspectorOrganizationUnitsData.map((item) => ({
      ...item,
      key: item.id,
    }));

    const inspectedRows = data.inspectedOrganizationUnitsData.map((item) => ({
      ...item,
      key: item.id,
    }));

    setRows([inspectorRows, inspectedRows]);
  };

  useEffectOnce(() => {
    fetchData();
  });

  return [rows, statuses];
};
