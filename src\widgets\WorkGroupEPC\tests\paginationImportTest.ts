/**
 * Тест импортов и типов для функциональности пагинации
 * Проверяет корректность всех импортов и типов без выполнения кода
 */

// Тест импортов типов
import type {
  TreePaginationParams,
  PaginatedTreeResponse,
  NodePaginationInfo,
  TreePaginationState,
  LoadMoreCallback,
} from '../types';

// Тест импортов констант
import {
  DEFAULT_PAGE_SIZE,
  MAX_PAGE_SIZE,
  LOAD_MORE_THRESHOLD,
  FIRST_PAGE,
  PAGINATION_CONFIG,
  ENABLE_PAGINATION,
  DEBUG_PAGINATION,
  PAGINATION_LOAD_DELAY,
} from '../config/constants';

// Тест импортов утилит
import {
  getNodePaginationConfig,
  shouldUsePaginationForNode,
  getPageSizeForNode,
  getNodePaginationDebugInfo,
  logPaginationInfo,
  validatePaginationParams,
  determineNodeType,
} from '../lib/paginationConfigHelper';

// Тест импортов хуков
import { useEPCTreeWithPagination } from '../store/hooks/useEPCTreeWithPagination';

// Тест импортов компонентов
import { NodeWithButtonsAndPagination } from '../ui/NodeWithButtonsAndPagination';
import { PaginationTestComponent } from '../ui/PaginationTestComponent';
import { PaginationIntegrationTest } from '../ui/PaginationIntegrationTest';

// Тест импортов из shared
import { LoadMoreButton } from 'shared/ui';
import { useLazyTreeDataWithPagination } from 'shared/model';
import { 
  createPaginatedTreeUrl,
  isPaginatedResponse,
  extractPaginationMetadata,
  createHeuristicPagination,
  shouldUsePagination,
  createPageParams,
} from 'shared/lib';

/**
 * Функция для проверки типов во время компиляции
 * Не выполняется в рантайме, только проверяет типы
 */
function typeChecks() {
  // Проверка типов пагинации
  const paginationParams: TreePaginationParams = {
    page: 1,
    pageSize: 10,
    nodeKey: 'test-key',
  };

  const paginationResponse: PaginatedTreeResponse<TreeElement> = {
    data: [],
    pagination: {
      currentPage: 1,
      totalPages: 5,
      pageSize: 10,
      totalItems: 50,
      hasMore: true,
    },
  };

  const nodeInfo: NodePaginationInfo = {
    currentPage: 1,
    totalPages: 5,
    hasMore: true,
    isLoadingMore: false,
    loadedItemsCount: 10,
    totalItemsCount: 50,
  };

  const paginationState: TreePaginationState = new Map();

  const loadMoreCallback: LoadMoreCallback = async (nodeKey: string) => {
    console.log(`Loading more for ${nodeKey}`);
    return [];
  };

  // Проверка констант
  const pageSize: number = DEFAULT_PAGE_SIZE;
  const maxSize: number = MAX_PAGE_SIZE;
  const threshold: number = LOAD_MORE_THRESHOLD;
  const firstPage: number = FIRST_PAGE;
  const enabled: boolean = ENABLE_PAGINATION;
  const debug: boolean = DEBUG_PAGINATION;
  const delay: number = PAGINATION_LOAD_DELAY;

  // Проверка конфигурации
  const config = PAGINATION_CONFIG.directory;
  const systemConfig = PAGINATION_CONFIG.systemDirectory;
  const largeConfig = PAGINATION_CONFIG.largeCatalog;

  // Проверка утилит
  const testNode: TreeElement = {
    key: 'test',
    title: 'Test Node',
    isDirectory: true,
    itemId: 'test-id',
  } as TreeElement;

  const nodeType = determineNodeType(testNode);
  const nodeConfig = getNodePaginationConfig(testNode);
  const shouldPaginate = shouldUsePaginationForNode(testNode);
  const nodePage = getPageSizeForNode(testNode);
  const debugInfo = getNodePaginationDebugInfo(testNode);
  const validation = validatePaginationParams(1, 10, testNode);

  // Проверка функций логирования
  logPaginationInfo(testNode, 'test');

  // Проверка shared утилит
  const url = createPaginatedTreeUrl('/api/tree', { page: 1, pageSize: 10, nodeKey: 'test' });
  const isPaginated = isPaginatedResponse({ data: [], pagination: { currentPage: 1 } });
  const metadata = extractPaginationMetadata({ data: [], pagination: { currentPage: 1 } });
  const heuristic = createHeuristicPagination([], 1, 10);
  const shouldUse = shouldUsePagination(testNode);
  const pageParams = createPageParams(1, 10);

  // Предотвращаем неиспользуемые переменные
  console.log({
    paginationParams,
    paginationResponse,
    nodeInfo,
    paginationState,
    loadMoreCallback,
    pageSize,
    maxSize,
    threshold,
    firstPage,
    enabled,
    debug,
    delay,
    config,
    systemConfig,
    largeConfig,
    nodeType,
    nodeConfig,
    shouldPaginate,
    nodePage,
    debugInfo,
    validation,
    url,
    isPaginated,
    metadata,
    heuristic,
    shouldUse,
    pageParams,
  });
}

/**
 * Функция для проверки доступности всех экспортов
 */
export function checkExports() {
  console.log('=== Pagination Import Test ===');
  
  // Проверяем константы
  console.log('Constants:', {
    DEFAULT_PAGE_SIZE,
    MAX_PAGE_SIZE,
    LOAD_MORE_THRESHOLD,
    FIRST_PAGE,
    ENABLE_PAGINATION,
    DEBUG_PAGINATION,
    PAGINATION_LOAD_DELAY,
  });

  // Проверяем конфигурацию
  console.log('Config:', PAGINATION_CONFIG);

  // Проверяем утилиты
  console.log('Utils available:', {
    getNodePaginationConfig: typeof getNodePaginationConfig,
    shouldUsePaginationForNode: typeof shouldUsePaginationForNode,
    getPageSizeForNode: typeof getPageSizeForNode,
    getNodePaginationDebugInfo: typeof getNodePaginationDebugInfo,
    logPaginationInfo: typeof logPaginationInfo,
    validatePaginationParams: typeof validatePaginationParams,
    determineNodeType: typeof determineNodeType,
  });

  // Проверяем хуки
  console.log('Hooks available:', {
    useEPCTreeWithPagination: typeof useEPCTreeWithPagination,
    useLazyTreeDataWithPagination: typeof useLazyTreeDataWithPagination,
  });

  // Проверяем компоненты
  console.log('Components available:', {
    NodeWithButtonsAndPagination: typeof NodeWithButtonsAndPagination,
    PaginationTestComponent: typeof PaginationTestComponent,
    PaginationIntegrationTest: typeof PaginationIntegrationTest,
    LoadMoreButton: typeof LoadMoreButton,
  });

  // Проверяем shared утилиты
  console.log('Shared utils available:', {
    createPaginatedTreeUrl: typeof createPaginatedTreeUrl,
    isPaginatedResponse: typeof isPaginatedResponse,
    extractPaginationMetadata: typeof extractPaginationMetadata,
    createHeuristicPagination: typeof createHeuristicPagination,
    shouldUsePagination: typeof shouldUsePagination,
    createPageParams: typeof createPageParams,
  });

  console.log('✅ All imports successful!');
}

// Экспортируем функцию проверки типов для использования в тестах
export { typeChecks };
