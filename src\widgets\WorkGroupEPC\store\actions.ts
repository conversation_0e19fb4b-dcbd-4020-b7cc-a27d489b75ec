import { DownloadBody, TreeMoveBody } from 'widgets/WorkGroupEPC';
import { appInstanceWithoutService } from 'shared/api';
import { createBasicGetThunk, createBasicPostThunk } from 'shared/lib';

export const getEpcTreeThunk = createBasicGetThunk<{ treeData: TreeElement[] }>(
  'workGroupEpc/getEpcTreeThunk',
);

export const postSearchThunk = createBasicPostThunk<
  { treeData: TreeElement[] },
  object
>('workGroupEpc/postSearchThunk');

export const postSearchLinkedElThunk = createBasicPostThunk<
  { treeData: TreeElement[] },
  object
>('workGroupEpc/postSearchLinkedElThunk');

export const postLinkedElementsThunk = createBasicPostThunk<
  { messages: Array<{ message: string; type: string }>; success: boolean },
  object
>('workGroupEpc/postLinkedElementsThunk');

export const postTreeMove = createBasicPostThunk<string, TreeMoveBody>(
  'workGroupEpc/postTreeMove',
);

export const postDeleteFiles = createBasicPostThunk<boolean, Key[]>(
  'workGroupEpc/postDeleteFiles',
);

export const getDownloaderLink = createBasicPostThunk<
  {
    data: string | null;
    messages: Array<{ message: string; type: string }>;
    success: boolean;
  },
  DownloadBody
>('workGroupEpc/getDownloaderLink', appInstanceWithoutService);
