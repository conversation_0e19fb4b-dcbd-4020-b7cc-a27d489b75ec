import type { ButtonWithCallback } from '..';

export const parseToCallbackButtons = (
  buttons: ButtonWithCallback[],
  callbackValue: string[],
  isPending?: boolean,
): AdditionalButton[] =>
  buttons.map((button) => ({
    ...Object.entries(button).reduce((obj, [key, value]) => {
      if (!['onRepoButtonClick', 'isCloseButton'].includes(key)) {
        obj[key as keyof AdditionalButton] = value;
      }

      return obj;
    }, {} as AdditionalButton),
    onClick: (event) =>
      button.onRepoButtonClick
        ? button.onRepoButtonClick(callbackValue)
        : 'onClick' in button && typeof button.onClick === 'function'
        ? button.onClick(event)
        : undefined,
    disabled:
      isPending || (!button.isCloseButton && callbackValue.length === 0),
    tooltip:
      callbackValue.length === 0 && !button.isCloseButton
        ? 'Необходимо выбрать репозитории'
        : undefined,
  }));
