import { CheckCircleTwoTone, CloseCircleOutlined } from '@ant-design/icons';
import { Tooltip, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import type { FC } from 'react';
import { DataGrid } from 'features/DataGrid';
import type { TableColumnData, TableRowData } from 'features/DataGrid';
import { useAppSelector } from 'shared/model';
import { AppResult } from 'shared/ui/AppResult';
import { AppSpinner } from 'shared/ui/AppSpinner';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import type { RepositorySyncItem, ButtonWithCallback } from '../..';

import { parseToCallbackButtons } from '../../lib';
import { selectors } from '../../store';
import styles from './styles.module.scss';

const renderTitle = (): JSX.Element => (
  <Typography.Text className={styles.title}>
    Выберите репозитории репликатора
  </Typography.Text>
);

export const RepositorySync: FC<{
  buttons: ButtonWithCallback[];
  getRepositorySync: Callback;
  isPending?: boolean;
  showSettings?: boolean;
}> = ({ showSettings, buttons, getRepositorySync, isPending }) => {
  const [checkedRepos, setCheckedRepos] = useState<string[]>([]);

  const { error, repos, ...repoSyncKeys } = useAppSelector(
    selectors.repositorySyncSelector,
  );

  const columns = useMemo<TableColumnData[]>(() => {
    const tempColumns: TableColumnData[] = [
      {
        key: 'repositoryId',
        dataIndex: 'repositoryId',
        title: 'Репозиторий',
        align: 'center',
      },
      {
        key: 'status',
        dataIndex: 'status',
        title: 'Статус',
        width: 90,
        hideColumnSearch: true,
        hideSorter: true,
        align: 'center',
        render: (text) => (
          <Tooltip title={text === 'SERVICE' ? 'Запущен' : 'Остановлен'}>
            {text === 'SERVICE' ? (
              <CheckCircleTwoTone twoToneColor="#52c41a" />
            ) : (
              <CloseCircleOutlined twoToneColor="#ff7d7d" />
            )}
          </Tooltip>
        ),
      },
    ];

    return !showSettings
      ? tempColumns
      : [
          ...tempColumns,
          {
            key: 'tasks',
            dataIndex: 'tasks',
            title: 'Текущее расписание',
            ellipsis: false,
            render: (_, row) => (
              <span>
                {Array.isArray(row.tasks) &&
                  (row.tasks as RepositorySyncItem['scheduler']['tasks']).map(
                    (task, index, arr) => (
                      <Typography.Text
                        code
                        title={task.cronDescription}
                        key={task.cronDescription}
                      >
                        {task.cronTriggerExpression}
                        {index !== arr.length - 1 && ' '}
                      </Typography.Text>
                    ),
                  )}
              </span>
            ),
          },
        ];
  }, [showSettings]);

  useEffect(getRepositorySync, []); // eslint-disable-line

  if (repoSyncKeys.isPending) {
    return <AppSpinner />;
  }

  if (error) {
    return (
      <AppResult
        title="Что то пошло не так, попробуйте обновить страницу"
        status={500}
      />
    );
  }

  if (repos.length === 0) {
    return (
      <AppResult
        title="Информация о репозиториях пока недоступна"
        status={403}
      />
    );
  }

  return (
    <div className={styles.container}>
      <DataGrid
        columns={columns}
        tableAdditionProps={{
          size: 'small',
          bordered: true,
          scroll: { x: '100%', y: 264 },
          title: renderTitle,
          rowSelection: {
            onChange: (selectedRepos) => {
              setCheckedRepos(selectedRepos as string[]);
            },
          },
        }}
        rows={repos.reduce((rows, repo) => {
          rows.push({
            key: repo.repositoryId,
            repositoryId: repo.repositoryId,
            status: repo.scheduler.status,
            ...(showSettings &&
              repo.scheduler.tasks && { tasks: repo.scheduler.tasks }),
          });

          return rows;
        }, [] as TableRowData[])}
      />

      <ButtonsContainer
        buttons={parseToCallbackButtons(buttons, checkedRepos, isPending)}
      />
    </div>
  );
};
