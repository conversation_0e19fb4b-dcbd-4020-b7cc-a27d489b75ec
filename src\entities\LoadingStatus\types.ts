export interface StatusResponse {
  current: number;
  currentFinishDate: string;
  isLoading: boolean;
  text: string;
  total: number;
}

export interface LoadingStatusState {
  /** Текущее значение */
  current: number;
  /** Дата окончания последней загрузки */
  currentFinishDate: string;
  /** Ошибка при загрузке данных */
  error: string | null;
  /** Состояние загрузки базы данных */
  isLoadingDatabase: boolean;
  /** Флаг статуса запроса на получение данных загрузки */
  isPending: boolean;
  /** Описание текущего статуса загрузки */
  text: string;
  /** Общее количество */
  total: number;
}
