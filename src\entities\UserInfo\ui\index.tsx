import { Space, Spin, Typography } from 'antd';
import { FC, memo } from 'react';
import { useEffectOnce } from 'react-use';
import { useAppSelector } from 'shared/model';
import { selectors } from '../store';

import styles from './styles.module.scss';

export const UserInfo: FC = memo(() => {
  const userName = useAppSelector(selectors.userNameSelector);
  const { isPending } = useAppSelector(selectors.userInfoApiSelector);

  useEffectOnce(() => {
    localStorage.setItem('userName', userName);
    return () => localStorage.removeItem('userName');
  });

  return (
    <Space direction="vertical" size={[0, 0]} className={styles.container}>
      <Typography.Text>Пользователь</Typography.Text>
      <Typography.Text strong className={styles.name}>
        {isPending ? <Spin size="small" /> : userName}
      </Typography.Text>
    </Space>
  );
});
