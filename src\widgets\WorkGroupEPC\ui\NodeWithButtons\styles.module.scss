.root {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;

  &Container {
    display: flex;
    gap: 5px;
    align-items: center;
  }

  &<PERSON><PERSON> {
    opacity: 0.6;
    transition: opacity 400ms linear;

    &:hover {
      opacity: 1;
    }
  }

  .skeleton * {
    margin: 3px 0 !important;
  }
}

.nodeContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loadMoreContainer {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
