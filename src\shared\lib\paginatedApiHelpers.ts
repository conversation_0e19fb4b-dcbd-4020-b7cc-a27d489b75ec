import { TreePaginationParams, PaginatedTreeResponse } from 'widgets/WorkGroupEPC';
import { generateUrlWithQueryParams } from './generateUrlWithQueryParams';

/**
 * Создает URL для пагинированного запроса дерева EPC
 * @param endpoint - Базовый endpoint
 * @param baseParams - Базовые параметры запроса (cabinetId, itemId, pos, isCabinet)
 * @param paginationParams - Параметры пагинации
 * @returns Сформированный URL с параметрами
 */
export const createPaginatedTreeUrl = (
  endpoint: string,
  baseParams: {
    cabinetId: string;
    itemId?: string;
    pos?: string;
    isCabinet?: boolean;
  },
  paginationParams?: TreePaginationParams,
): string => {
  const allParams = {
    ...baseParams,
    ...(paginationParams && {
      page: paginationParams.page,
      pageSize: paginationParams.pageSize,
      // Добавляем флаг для указания, что нужна пагинация
      enablePagination: true,
    }),
  };

  return generateUrlWithQueryParams(endpoint, allParams, { parseAllValues: true });
};

/**
 * Проверяет, поддерживает ли ответ сервера пагинацию
 * @param response - Ответ от сервера
 * @returns true, если ответ содержит метаданные пагинации
 */
export const isPaginatedResponse = (
  response: any,
): response is PaginatedTreeResponse => {
  return (
    response &&
    typeof response === 'object' &&
    'treeData' in response &&
    'pagination' in response &&
    typeof response.pagination === 'object' &&
    'currentPage' in response.pagination &&
    'totalPages' in response.pagination &&
    'hasMore' in response.pagination
  );
};

/**
 * Извлекает метаданные пагинации из ответа сервера
 * @param response - Ответ от сервера
 * @returns Метаданные пагинации или null, если их нет
 */
export const extractPaginationMetadata = (
  response: any,
): PaginatedTreeResponse['pagination'] | null => {
  if (isPaginatedResponse(response)) {
    return response.pagination;
  }
  return null;
};

/**
 * Создает эвристическую информацию о пагинации на основе количества элементов
 * Используется как fallback, когда сервер не возвращает метаданные пагинации
 * @param itemsCount - Количество полученных элементов
 * @param pageSize - Размер страницы
 * @param currentPage - Текущая страница
 * @returns Эвристические метаданные пагинации
 */
export const createHeuristicPagination = (
  itemsCount: number,
  pageSize: number,
  currentPage: number,
): PaginatedTreeResponse['pagination'] => {
  // Если получили меньше элементов, чем размер страницы, 
  // предполагаем, что это последняя страница
  const hasMore = itemsCount >= pageSize;
  
  return {
    currentPage,
    totalPages: hasMore ? currentPage + 1 : currentPage, // Минимальная оценка
    pageSize,
    totalItems: hasMore ? (currentPage * pageSize) + 1 : (currentPage - 1) * pageSize + itemsCount, // Минимальная оценка
    hasMore,
  };
};

/**
 * Определяет, нужно ли использовать пагинацию для узла дерева
 * @param node - Узел дерева
 * @param threshold - Пороговое значение количества дочерних элементов
 * @returns true, если для узла нужна пагинация
 */
export const shouldUsePagination = (
  node: TreeElement,
  threshold: number = 10,
): boolean => {
  // Пагинация нужна только для директорий
  if (!node.isDirectory) {
    return false;
  }

  // Если есть информация о количестве дочерних элементов
  if (typeof node.totalCountOfLeafs === 'number') {
    return node.totalCountOfLeafs > threshold;
  }

  // Если есть информация о количестве детей первого уровня
  if (typeof node.childrenCount === 'number') {
    return node.childrenCount > threshold;
  }

  // По умолчанию не используем пагинацию
  return false;
};

/**
 * Создает параметры пагинации для первой страницы
 * @param pageSize - Размер страницы
 * @returns Параметры пагинации для первой страницы
 */
export const createFirstPageParams = (pageSize: number): TreePaginationParams => ({
  page: 1,
  pageSize,
});

/**
 * Создает параметры пагинации для следующей страницы
 * @param currentPage - Текущая страница
 * @param pageSize - Размер страницы
 * @returns Параметры пагинации для следующей страницы
 */
export const createNextPageParams = (
  currentPage: number,
  pageSize: number,
): TreePaginationParams => ({
  page: currentPage + 1,
  pageSize,
});
