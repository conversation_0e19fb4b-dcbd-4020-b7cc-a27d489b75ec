import { generateUrlWithQueryParams } from './generateUrlWithQueryParams';

/**
 * Создает URL для пагинированного запроса дерева EPC
 * @param endpoint - Базовый endpoint
 * @param baseParams - Базовые параметры запроса (cabinetId, itemId, pos, isCabinet)
 * @param page - Номер страницы
 * @param pageSize - Размер страницы
 * @returns Сформированный URL с параметрами
 */
export const createPaginatedTreeUrl = (
  endpoint: string,
  baseParams: {
    cabinetId: string;
    isCabinet?: boolean;
    itemId?: string;
    pos?: string;
  },
  page?: number,
  pageSize?: number,
): string => {
  const allParams = {
    ...baseParams,
    ...(page && pageSize && {
      page,
      pageSize,
    }),
  };

  return generateUrlWithQueryParams(endpoint, allParams, {
    parseAllValues: true
  });
};

/**
 * Определяет, есть ли еще элементы для загрузки на основе количества полученных элементов
 * @param itemsCount - Количество полученных элементов
 * @param pageSize - Размер страницы
 * @returns true, если есть еще элементы для загрузки
 */
export const hasMoreItems = (itemsCount: number, pageSize: number): boolean =>
  itemsCount >= pageSize;
