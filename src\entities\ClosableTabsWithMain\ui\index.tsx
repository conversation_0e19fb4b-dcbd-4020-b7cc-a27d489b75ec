import { Tabs } from 'antd';
import { FC } from 'react';
import type { ClosableTabsWithMainProps } from '..';

import { FIRST_TAB } from '../config';

import styles from './styles.module.scss';

export const ClosableTabsWithMain: FC<ClosableTabsWithMainProps> = ({
  title,
  MainTab,
  activeKey,
  onClose,
  onTabClick,
  additionalTabs,
}) => (
  <Tabs
    type="editable-card"
    hideAdd
    className={styles.tabs}
    activeKey={activeKey}
    onTabClick={onTabClick}
    items={[
      {
        closable: false,
        key: FIRST_TAB,
        label: title,
        children: MainTab,
      },
      ...additionalTabs,
    ]}
    onEdit={(key, action) => {
      if (action === 'remove' && typeof key === 'string') {
        onClose(key, activeKey);
      }
    }}
  />
);
