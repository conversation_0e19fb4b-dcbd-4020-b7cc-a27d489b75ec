/* Функция для проверки существования сервиса */
const checkMockServiceWorkerMimeType = async (): Promise<boolean> => {
  try {
    const response = await fetch('/mockServiceWorker.js');
    const contentType = response.headers.get('content-type');
    return (
      !!contentType?.includes('javascript') ||
      !!contentType?.includes('application/js')
    );
  } catch (error) {
    return false;
  }
};

export const initMocks = async (): Promise<void> => {
  try {
    const isMimeTypeValid = await checkMockServiceWorkerMimeType();

    if (!isMimeTypeValid) {
      // eslint-disable-next-line no-console
      console.warn('Please run "npm run init:msw" to set up MSW properly.');
      return;
    }

    const { worker } = await import('./browser');
    await worker.start({
      onUnhandledRequest: 'bypass',
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to initialize mocks:', error);
    throw error;
  }
};
