import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, Table, Typography, Space, Alert, Switch, Statistic, Row, Col } from 'antd';
import { PerformanceMonitor, caches } from '../../lib/paginationPerformanceOptimizer';
import { DEBUG_PAGINATION } from '../../config/constants';

const { Title, Text } = Typography;

interface PerformanceStats {
  key: string;
  operation: string;
  avg: number;
  min: number;
  max: number;
  count: number;
}

/**
 * Панель мониторинга производительности пагинации
 * Показывает статистику производительности и состояние кэшей
 */
export const PerformanceMonitorPanel: React.FC = () => {
  const [stats, setStats] = useState<PerformanceStats[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Обновление статистики
  const updateStats = useCallback(() => {
    const allStats: PerformanceStats[] = [];
    
    // Получаем все доступные измерения
    const measurements = (PerformanceMonitor as any).measurements;
    if (measurements && measurements instanceof Map) {
      for (const [key] of measurements) {
        const stat = PerformanceMonitor.getStats(key);
        if (stat) {
          allStats.push({
            key,
            operation: key.replace(/_/g, ' '),
            avg: Math.round(stat.avg * 100) / 100,
            min: Math.round(stat.min * 100) / 100,
            max: Math.round(stat.max * 100) / 100,
            count: stat.count,
          });
        }
      }
    }
    
    setStats(allStats);
  }, []);

  // Автообновление
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(updateStats, 2000);
      setRefreshInterval(interval);
      return () => clearInterval(interval);
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [autoRefresh, updateStats]);

  // Очистка статистики
  const handleClearStats = useCallback(() => {
    PerformanceMonitor.clear();
    setStats([]);
  }, []);

  // Очистка кэшей
  const handleClearCaches = useCallback(() => {
    caches.clear();
    console.log('All caches cleared');
  }, []);

  // Логирование всей статистики в консоль
  const handleLogStats = useCallback(() => {
    PerformanceMonitor.logAllStats();
  }, []);

  // Колонки таблицы статистики
  const columns = [
    {
      title: 'Операция',
      dataIndex: 'operation',
      key: 'operation',
      width: '30%',
    },
    {
      title: 'Среднее (мс)',
      dataIndex: 'avg',
      key: 'avg',
      width: '15%',
      render: (value: number) => <Text code>{value}</Text>,
    },
    {
      title: 'Минимум (мс)',
      dataIndex: 'min',
      key: 'min',
      width: '15%',
      render: (value: number) => <Text code>{value}</Text>,
    },
    {
      title: 'Максимум (мс)',
      dataIndex: 'max',
      key: 'max',
      width: '15%',
      render: (value: number) => <Text code>{value}</Text>,
    },
    {
      title: 'Количество',
      dataIndex: 'count',
      key: 'count',
      width: '15%',
      render: (value: number) => <Text strong>{value}</Text>,
    },
  ];

  // Получение размеров кэшей
  const getCacheStats = () => ({
    nodeConfig: caches.nodeConfig.size,
    nodeType: caches.nodeType.size,
    paginationNeed: caches.paginationNeed.size,
  });

  const cacheStats = getCacheStats();

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>Мониторинг производительности пагинации</Title>
      
      <Alert
        message="Панель разработчика"
        description="Эта панель предназначена для мониторинга производительности пагинации во время разработки. В продакшене она должна быть удалена."
        type="warning"
        showIcon
        style={{ marginBottom: '20px' }}
      />

      {!DEBUG_PAGINATION && (
        <Alert
          message="Отладка отключена"
          description="Для получения подробной статистики включите DEBUG_PAGINATION в константах."
          type="info"
          showIcon
          style={{ marginBottom: '20px' }}
        />
      )}

      {/* Панель управления */}
      <Card title="Управление мониторингом" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button type="primary" onClick={updateStats}>
            Обновить статистику
          </Button>
          
          <Button onClick={handleClearStats}>
            Очистить статистику
          </Button>
          
          <Button onClick={handleClearCaches}>
            Очистить кэши
          </Button>
          
          <Button onClick={handleLogStats}>
            Логировать в консоль
          </Button>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text>Автообновление:</Text>
            <Switch 
              checked={autoRefresh} 
              onChange={setAutoRefresh}
              size="small"
            />
          </div>
        </Space>
      </Card>

      {/* Статистика кэшей */}
      <Card title="Состояние кэшей" style={{ marginBottom: '20px' }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic 
              title="Конфигурация узлов" 
              value={cacheStats.nodeConfig}
              suffix="записей"
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="Типы узлов" 
              value={cacheStats.nodeType}
              suffix="записей"
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="Необходимость пагинации" 
              value={cacheStats.paginationNeed}
              suffix="записей"
            />
          </Col>
        </Row>
      </Card>

      {/* Таблица статистики производительности */}
      <Card title="Статистика производительности">
        {stats.length > 0 ? (
          <Table
            columns={columns}
            dataSource={stats}
            pagination={false}
            size="small"
            scroll={{ y: 400 }}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">
              Нет данных о производительности. 
              Выполните операции с деревом для сбора статистики.
            </Text>
          </div>
        )}
      </Card>

      {/* Рекомендации по оптимизации */}
      {stats.length > 0 && (
        <Card title="Рекомендации по оптимизации" style={{ marginTop: '20px' }}>
          <Space direction="vertical">
            {stats.some(s => s.avg > 100) && (
              <Alert
                message="Медленные операции"
                description="Обнаружены операции со временем выполнения более 100мс. Рассмотрите возможность дополнительной оптимизации."
                type="warning"
                showIcon
              />
            )}
            
            {cacheStats.nodeConfig > 500 && (
              <Alert
                message="Большой размер кэша"
                description="Кэш конфигурации узлов содержит много записей. Возможно, стоит увеличить частоту очистки."
                type="info"
                showIcon
              />
            )}
            
            {stats.some(s => s.count > 1000) && (
              <Alert
                message="Частые операции"
                description="Некоторые операции выполняются очень часто. Проверьте, нет ли избыточных вызовов."
                type="info"
                showIcon
              />
            )}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default PerformanceMonitorPanel;
