.sortTitle {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.sortCarets {
  display: flex;
  flex-direction: column;
  margin-left: 5px;
  opacity: 0.7;
  transition: opacity 200ms linear;

  &:hover {
    opacity: 1;
  }
}

.sortCaret {
  font-size: 10px;
  color: #bfbfbf !important;

  &_active {
    opacity: 1;
    color: #1890ff !important;
  }

  &_hidden {
    opacity: 0.6;
  }
}
