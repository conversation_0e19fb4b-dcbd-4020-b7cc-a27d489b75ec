import { TableColumnData } from '..';

/**
 * Добавление хендлера лези-лоад сортировки на колонки
 *
 * @function parseToLazyLoadSort
 * @param columns Массив колонок
 * @param onSort Колбек для проброса нового значения сортировки и текущей
 *   колонки
 * @param conditionForRender Колбек с булевым для рендера колонки
 * @param [sortOrderOptions] Опциональный обьект для проброса параметра
 *   сортировки
 * @param sortOrderOptions.columnId UUid колонки
 * @param sortOrderOptions.sortOrder Новое значение сортировки
 * @returns Массив отпаршеных колонок
 */
export const parseToLazyLoadSort = (
  columns: TableColumnData[],
  onSort: (
    column: TableColumnData,
    newSortOrder: TableColumnData['lazySortOrder'],
  ) => void,
  conditionForRender: (column: TableColumnData) => boolean,
  sortOrderOptions?: {
    columnId: string;
    sortOrder: TableColumnData['lazySortOrder'];
  },
): TableColumnData[] =>
  columns.map((column) => {
    if (conditionForRender(column)) {
      return {
        ...column,
        onLazyLoadSort: (newSortOrder) => {
          onSort(column, newSortOrder);
        },
        /* Прокидывание нового сорт ордера */
        ...(sortOrderOptions &&
          sortOrderOptions.columnId === column.columnUuid && {
            lazySortOrder: sortOrderOptions.sortOrder,
          }),
      };
    }

    return column;
  });
