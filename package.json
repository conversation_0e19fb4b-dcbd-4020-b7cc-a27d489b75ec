{"name": "ppod-kzid", "version": "1.0.1", "private": true, "dependencies": {"@ant-design/icons": "4.8.1", "@reduxjs/toolkit": "1.9.7", "antd": "4.24.15", "axios": "0.26.1", "classnames": "2.3.1", "compose-function": "3.0.3", "framer-motion": "6.5.1", "gh-pages": "4.0.0", "history": "5.3.0", "moment": "2.29.4", "mq-polyfill": "1.1.8", "prettier-plugin-jsdoc": "0.4.2", "rc-virtual-list": "3.4.10", "react": "17.0.2", "react-dom": "17.0.2", "react-error-boundary": "3.1.4", "react-redux": "7.2.9", "react-resizable": "3.0.4", "react-router": "6.8.1", "react-router-dom": "6.8.1", "react-scripts": "5.0.0", "react-use": "17.4.0", "react-virtualized-auto-sizer": "1.0.22", "redux": "4.2.1", "redux-logger": "3.0.6", "sass": "1.54.4", "uuid": "8.3.2"}, "scripts": {"start": "react-scripts start", "prebuild": "node node_modules/gh-pages/bin/gh-pages-clean", "build": "cross-env CI=false react-scripts build", "build:zt": "cross-env REACT_APP_ZT=true CI=false react-scripts build", "clear:build": "rm -rf ./build", "init:msw": "npx msw init public/ --save", "analyze": "react-scripts build --stats && webpack-bundle-analyzer build/bundle-stats.json", "test": "react-scripts test --watchAll=false -u", "test:cbant": "react-scripts test --watchAll=false -u --transformIgnorePatterns \"node_modules/(?!@cbkit)/\"", "test:cbkit": "react-scripts test --watchAll=false -u", "test:coverage": "npm test -- --coverage --collectCoverageFrom=\"./src/**\"", "start:spa": "cross-env CLIENT_ENV=production craco start", "build:spa": "cross-env CLIENT_ENV=production craco build", "lint": "eslint . --fix && stylelint \"**/*.scss\" --fix", "prettier": "prettier ./src -w", "prepare": "husky install", "predeploy": "npm run build", "deploy": "gh-pages -d build -b build && npm run clear:build && npm run deploy:zt", "deploy:zt": "npm run prebuild && cross-env REACT_APP_ZT=true CI=false react-scripts build && gh-pages -d build -b build-zt && npm run clear:build", "postdeploy": "npm run clear:build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "jest": {"transformIgnorePatterns": ["node_modules/(?!@cbkit)/"]}, "lint-staged": {"*.(ts|tsx)": ["prettier --write", "eslint --fix"], "*.(scss)": ["prettier --write", "stylelint --fix"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@craco/craco": "7.0.0", "@feature-sliced/eslint-config": "0.1.0-beta.6", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.4.3", "@types/compose-function": "0.0.30", "@types/react": "17.0.66", "@types/react-dom": "18.0.9", "@types/react-redux": "7.1.24", "@types/react-resizable": "3.0.2", "@types/react-test-renderer": "17.0.2", "@types/redux-logger": "3.0.9", "@types/uuid": "8.3.4", "@typescript-eslint/eslint-plugin": "5.33.1", "@typescript-eslint/parser": "5.33.1", "core-js": "3.24.1", "craco-plugin-single-spa-application": "2.0.1", "cross-env": "7.0.3", "css-loader": "6.7.1", "dotenv-cra": "3.0.2", "eslint": "8.22.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.5.0", "eslint-import-resolver-typescript": "2.7.1", "eslint-plugin-boundaries": "2.10.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-no-inline-styles": "1.0.5", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.30.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "0.6.4", "eslint-plugin-typescript-sort-keys": "2.1.0", "eslint-plugin-unused-imports": "2.0.0", "husky": "8.0.1", "jest-fetch-mock": "3.0.3", "lint-staged": "12.5.0", "mini-css-extract-plugin": "2.6.1", "msw": "2.7.0", "prettier": "2.7.1", "react-docgen-typescript": "2.2.2", "react-test-renderer": "17.0.2", "single-spa": "5.9.4", "single-spa-react": "4.6.1", "style-loader": "3.3.1", "stylelint": "14.10.0", "stylelint-config-prettier": "9.0.3", "stylelint-config-prettier-scss": "0.0.1", "stylelint-config-recess-order": "3.0.0", "stylelint-config-standard": "26.0.0", "stylelint-config-standard-scss": "3.0.0", "stylelint-order": "5.0.0", "stylelint-scss": "4.3.0", "typescript": "4.8.2", "webpack": "5.76.2", "webpack-bundle-analyzer": "4.5.0", "webpack-cli": "4.10.0"}, "msw": {"workerDirectory": ["public"]}}