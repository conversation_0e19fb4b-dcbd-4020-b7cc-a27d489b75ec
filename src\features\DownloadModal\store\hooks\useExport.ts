import moment from 'moment/moment';
import { useCallback } from 'react';
import { apiUrls, postForBlobFile } from 'shared/api';
import { DEFAULT_DATE_TIME_VARIANT } from 'shared/config/constants';
import { asyncDownloadFile, generateUrlWithQueryParams } from 'shared/lib';

export const useExport = (
  url: Endpoint,
  isExportToExcel: boolean,
  reportControlDTO: ReportControlDTO,
): Callback =>
  useCallback(() => {
    if (isExportToExcel) {
      asyncDownloadFile(
        postForBlobFile<ReportControlDTO>(
          generateUrlWithQueryParams(apiUrls.download.excelExport(url), {}),
          reportControlDTO,
          `Отчет ${moment().format(DEFAULT_DATE_TIME_VARIANT)}.xlsx`,
        ),
      );
    }
  }, [isExportToExcel, reportControlDTO, url]);
