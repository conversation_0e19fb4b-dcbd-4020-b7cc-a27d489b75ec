import { Tree } from 'antd';
import { FC, useCallback, useMemo, useState } from 'react';
import {
  WorkGroupEPCLib,
  EpcPermissions,
  UseTreeTypes,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { renderTreeTitle, useAppSelector } from 'shared/model';
import { AppPopup, ButtonsContainer } from 'shared/ui';
import styles from './styles.module.scss';

type CopyModalWithTreeInnerProps = {
  cabinetId: string;
  epcPermissions: EpcPermissions;
  handleClose: Callback;
  refetchNode: (node: TreeElement) => void;
  selectedFiles: TreeElement[];
  selectedNodes: TreeElement[];
} & Partial<UseTreeTypes>;

type CopyModalWithTreeProps = CopyModalWithTreeInnerProps & {
  isOpened: boolean;
};

const initialCheck = { key: [], itemId: '', isLoaded: false } as {
  itemId: string;
  key: Key[];
};

const CopyModalWithTreeInner: FC<CopyModalWithTreeInnerProps> = ({
  handleClose,
  selectedFiles,
  refetchNode,
  cabinetId,
  selectedNodes,
  epcPermissions,
  treeLoad,
  treeExpand,
}) => {
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);
  const [check, setCheck] = useState(initialCheck);
  const [saveFiles, stateSaveFiles] = WorkGroupEPCStore.hooks.useCopyFile(
    cabinetId,
    check.itemId,
  );

  const parsedSelectedNodes = useMemo(
    () =>
      (selectedNodes as (TreeElement & { fullCopy: boolean })[]).map((node) => {
        if (!node.isDirectory) {
          return node;
        }

        if (treeLoad?.loadedKeysSet.has(node.key)) {
          node.fullCopy = false;
        } else {
          node.fullCopy = true;
        }

        return node;
      }),
    [selectedNodes, treeLoad?.loadedKeysSet],
  );

  const parents = useMemo(() => {
    const uniqParents = new Set<string>(
      [...selectedFiles, ...selectedNodes]
        .filter((node) => node.parent)
        .map(({ parent }) => parent) as string[],
    );

    return uniqParents;
  }, [selectedFiles, selectedNodes]);

  const buttons = WorkGroupEPCStore.hooks.useCopyModalButtons(
    {
      itemId: check.itemId,
      key: String(check.key),
      isDirectory: true,
    } as TreeElement,
    handleClose,
    parsedSelectedNodes,
    check.key.length === 0,
    saveFiles,
    stateSaveFiles.isPending,
    stateSaveFiles.data,
    refetchNode,
    epcPermissions,
  );

  const parsedTree = useMemo(
    () =>
      WorkGroupEPCLib.modifyAndFilterTreeByConfigs(
        epc.treeData.tree,
        (node) => Boolean(node.isDirectory) && node.isFixed !== 2,
        (node) => {
          const fields: Partial<TreeElement> = {};
          const isLeaf =
            node.children &&
            node?.children?.length > 0 &&
            node?.children?.every(
              (item) => !item.isDirectory || item.isFixed === 2,
            );

          if (
            parents.has(node?.itemId || '') ||
            selectedNodes.some(({ itemId }) => itemId === node?.itemId || '')
          ) {
            fields.checkable = false;
            fields.isLeaf = true;
          }

          if (isLeaf) fields.isLeaf = true;
          return fields;
        },
      ),
    [epc.treeData.tree, parents, selectedNodes],
  );

  const handleCheck = useCallback(
    (_: unknown, info: { node: TreeElement }): void => {
      setCheck(
        info.node.checked
          ? initialCheck
          : { key: [info.node.key], itemId: info.node?.itemId || '' },
      );
    },
    [],
  );

  return (
    <div className={styles.content}>
      <Tree
        checkable
        selectable={false}
        loadedKeys={treeLoad?.loadedKeys}
        checkedKeys={check.key}
        checkStrictly
        onCheck={handleCheck}
        treeData={parsedTree}
        autoExpandParent={treeExpand?.autoExpandParent}
        expandedKeys={treeExpand?.expandedKeys}
        loadData={treeLoad?.onLoadData}
        onExpand={treeExpand?.handleExpand}
        height={400}
        showLine
        titleRender={renderTreeTitle}
      />
      <ButtonsContainer buttons={buttons} />
    </div>
  );
};

export const CopyModalWithTree: FC<CopyModalWithTreeProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    isOpened={isOpened}
    onClose={handleClose}
    title="Вставить файлы"
    className={styles.popup}
  >
    <CopyModalWithTreeInner {...props} handleClose={handleClose} />
  </AppPopup>
);
