import { notification } from 'antd';
import { useCallback } from 'react';
import { TableRowData } from 'features/DataGrid';
import { createConfirmModal } from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';

type HandleOpenConfirmWithAction = (
  url: Endpoint,
  messages: Record<'title' | 'subTitle' | 'notice', string>,
  selectedRows: TableRowData[],
) => Promise<void>;

export const useOpenConfirmWithAction = (
  onRefresh?: Callback,
  onClear?: Callback,
): [HandleOpenConfirmWithAction, boolean] => {
  const [trigger, { isPending }] = useAxiosRequest();

  const handleOpenConfirmWithAction = useCallback<HandleOpenConfirmWithAction>(
    (url, messages, selectedRows) => {
      const { title, subTitle, notice } = messages;

      return new Promise((resolve, reject) => {
        createConfirmModal({
          title,
          message: subTitle,
          onConfirm: () =>
            trigger(url, {
              method: 'POST',
              data: selectedRows.map(
                (item) => (item.rowId as { rowId: string }).rowId,
              ),
            })
              .then(() => {
                notification.success({
                  message: notice,
                });
                if (onClear) {
                  onClear();
                }
                if (onRefresh) {
                  onRefresh();
                }

                resolve();
              })
              .catch(reject),
        });
      });
    },
    [], // eslint-disable-line
  );

  return [handleOpenConfirmWithAction, isPending];
};
