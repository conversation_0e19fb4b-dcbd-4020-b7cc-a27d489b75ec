/// <reference types="react-scripts" />

declare module '*.module.less' {
  const classes: { [key: string]: string };
  export default classes;
}

declare interface TableColumnsAndRows {
  columns: import('features/DataGrid').TableColumnData[];
  rows: import('features/DataGrid').TableRowData[];
}

declare type TableColumnsAndRowsWithPagination = TableColumnsAndRows & {
  pagination: {
    pageSize: number;
    total: number;
  };
  pageNumber: number;
};

declare interface TreeElement {
  /** Ключ */
  key: Key;
  /** Текст дерева */
  title: Title | import('react').ReactNode;
  /** Если элемент используется для отображения загрузки */
  isSkeleton?: boolean;
  /** Ширина элемента */
  width?: number;
  /** Цвет текста */
  color?: string;
  /** Дети */
  children?: TreeElement[];
  /** Айди, нужен для лези запросов */
  itemId?: string;
  /** Флаг наличия связанных каталогов и файлов */
  hasLinked?: boolean;
  /** Проверка на то можно ли провалится дальше в детей */
  isLeaf?: boolean;
  /** Позиция элемента */
  pos?: string;
  /** Значение, нужно для хардовой отрисовки иконки папки */
  isDirectory?: boolean;
  /** Кастомный дизейбл ноды */
  isDisabled?: boolean;
  /** Всего вложенных листьев (файлов) */
  totalCountOfLeafs?: number;
  /** Ключ на показ основного файла */
  isMainFile?: boolean;
  /** Дополнительная иконка */
  icon?: JSX.Element;
  /** Вызываемые дети */
  isCallable?: boolean;
  /** Имеет ли чекбокс */
  checkable?: boolean;
  /** Количество детей 1-уровня любого типа */
  childrenCount?: number;
  /** Найденое значение */
  tagged?: boolean;
  /** Стейт чекбокса */
  checked?: boolean;
  /** <AUTHOR> Не заполнил */
  fullPath?: string;
  /** Айди родителя */
  parentId?: string;
  /** Айди файла в файлнете */
  fileNetId?: string;
  /** Статус ноды */
  disabled?: boolean;
  /** Массив кодов вопросов */
  questions?: string[];
  /** Массив линков */
  linked?: TreeElement[];
  /** Может ли быть открыто в визуализаторе */
  canView?: boolean;
  /** Айди досье проверки */
  dossierId?: string;
  /** Айди карточки файла */
  cardId?: string;
  /** Родительский айди */
  parent?: string;
  /** Есть ли файл с подписью */
  fileSignExist?: boolean;
  /** Можно ли удалить */
  isRemovable?: boolean;
  position?: number;
  /** Привязка к заявке, пункту заявки или уведомлению */
  requestNoticeId?: string | null;
  /** Привязка к пакету ДЭФ */
  dataPackageId?: string | null;
  /** Привязка к уведомлению внутренней переписки */
  internalNoticeId?: string | null;
  /** Есть ли права на действия с файлами */
  permissions?: Record<'canView' | 'canDownload', boolean>;
  /** Имя каталога без маркировки */
  rawTitle?: string;
  systemHint?: string;
  isFixed?: 0 | 1 | 2 | 3;
  isMain?: 0 | 1 | boolean;
  /** Файлы только для крг */
  cabinetOnly?: boolean;
  /** Айдишники детей ноды */
  childrenIds?: string[];
  /** Если элемент используется для отображения таблицы */
  isTable?: boolean;
  /** Данные вложенной таблицы */
  tableData?: TableElementForLazyTree;
  /** Количество файлов в папке */
  countOfFolderFiles?: number;

  // ============================================================================
  // PAGINATION PROPERTIES FOR LOAD MORE FUNCTIONALITY
  // ============================================================================

  /** Есть ли еще элементы для загрузки (для Load More функциональности) */
  hasMore?: boolean;
  /** Текущая загруженная страница */
  currentPage?: number;
  /** Общее количество страниц */
  totalPages?: number;
  /** Идет ли загрузка дополнительных элементов */
  isLoadingMore?: boolean;
  /** Количество уже загруженных дочерних элементов */
  loadedItemsCount?: number;
}

declare interface TableElementForLazyTree {
  filters: import('features/SimpleColumnFilters').ColumnFilters[];
  // pagination: { pageSize: number; total: number };
  rows: import('features/DataGrid').TableRowData[];
  currentPage: number;
}

declare interface MoveDNDBody {
  itemId: string;
  parentId: string;
  position: number;
}

declare interface CheckBoxElement {
  /** Заголовок чекбокса */
  title: string;
  /** Состояние чекбокса */
  checked: boolean;
  /** Доступен ли чекбокс */
  enable?: boolean;
  /** Виден ли чекбокс */
  visible?: boolean;
}

/** Заголовок */
declare type Title = string;

declare type Key = React.Key;

/** Кастомный тип ошибки */
declare type AppError =
  | string
  | import('@reduxjs/toolkit').SerializedError
  | import('axios').AxiosError
  | null;

declare type DataId = string;

/** Тип для параметров */
declare type RequestParam = string;

/** Тип со стандарным значением ключ и титул */
declare interface TitleAndKey {
  /** Титул */
  title: Title;
  /** Ключ */
  key: Key;
}

/** Тип для всех ендпоинтов */
declare type Endpoint = string;

/** Дефолтный тип колбека без параметров */
declare type Callback = () => void;

/** Дефолтный тип асинхронного колбека без параметров */
declare type PromiseCallback = () => Promise<void>;

declare type Setter<T> = import('react').Dispatch<
  import('react').SetStateAction<T | undefined>
>;

declare type AdditionalButton = {
  hidden?: boolean;
  tooltip?: string;
  tooltipProps?: import('antd/lib/tooltip').TooltipProps;
  badgeProps?: import('antd/lib/badge').BadgeProps;
} & import('antd/lib/button/button').ButtonProps &
  TitleAndKey;

declare type NonEmptyArray<T> = [T, ...T[]];

declare type EventValue<DateType> = DateType | null;
declare type RangeValue<DateType> =
  | [EventValue<DateType>, EventValue<DateType>]
  | null;

declare interface SelectData extends TitleAndKey {
  /** Значение */
  value: string;
  /** @link Title */
  label?: Title;
  /** Дефолтное значение */
  isSelected?: boolean;
}

/** Маппинг объекта с [ключ]: значение */
declare type MapObject<Type> = {
  [Property in keyof Type]: Type[Property];
};

/** Маппинг в объект экшенов */
declare type ParseActions<T> = {
  [Property in keyof T]: Parameters<T[Property]>[0] extends undefined
    ? Callback
    : (payload: Parameters<T[Property]>[0]) => void;
};

declare type AppTab = import('rc-tabs/lib/interface').Tab;

declare interface ApiDefaultKeys {
  isPending: boolean;
  error: AppError;
}

declare interface SearchFormField {
  key: Key;
  placeholder: string;
  title: string;
  type: 'input' | 'select' | 'datePicker' | 'rangePicker' | 'multipleSelect';
  name: string;
  selectData?: SelectData[];
}

declare interface ContextMenuItem {
  key: Key;
  onClick?: Callback;
  label: Title;
  children?: ContextMenuItem[];
}

declare interface CustomWindow extends Window {
  frontDebug: Callback;
  sidebarDebug: Callback;
}

declare interface TableRowSelection {
  keys: Key[];
  rows: import('features/DataGrid').TableRowData[];
}

declare interface HeaderData {
  data: {
    content: string;
    isEditable: boolean;
    isLink: boolean;
    key: string;
    title: string;
  }[];
  key: string;
  title: string;
}

/**
 * Интерфейс для прокидывания инфы по фильтрам, сортировке и настройкам для
 * Джаспера
 */
declare interface ReportControlDTO {
  /** Список id кеолонки и варианта сортировки */
  idAndOrders?: { columnId: string; descending: boolean }[];
  /** Фильтры */
  inputData?: import('widgets/FiltersDrawer').PanelData[];
  /** Какие либо настройки для JasperReports */
  reportSettings?: Record<string, unknown>;
  /** Колонки для отчета Excel */
  columns?: Partial<import('features/DataGrid').TableColumnData>[];
}

/** Типизация сортировки колнок */
declare type SortOrder = 'DESC' | 'ASC';

declare type Files = import('antd/es/upload').RcFile & {
  /** Чек */
  checked?: boolean;
  /** Редактируемость */
  editable?: boolean;
};
