import { UploadFile } from 'antd/es/upload/interface';

export type Files = UploadFile<boolean> & {
  /** Редактируемость */
  checked?: boolean;
  editable?: boolean;
};

export interface UploadFileListProps {
  fileArr: Files[];
  handleDeleteFile: (deleteId: string) => void;
  checkable?: Checkable;
  uploadState?: UploadState;
}

interface Checkable {
  error: AppError;
  handleCheckAllFiles: (checked: boolean) => void;
  handleCheckFiles: (file: Files) => void;
  isPending: boolean;
}

export interface UploadState {
  fileId: string;
  progress: number;
}
