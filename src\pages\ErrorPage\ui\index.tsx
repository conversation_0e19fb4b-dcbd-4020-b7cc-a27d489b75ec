import { Button } from 'antd';
import { ResultStatusType } from 'antd/lib/result';
import { FC } from 'react';
import { composeMailto } from 'shared/lib';

import { AppResult } from 'shared/ui/AppResult';
import { PageContainer } from 'shared/ui/PageContainer';
import styles from './styles.module.scss';

export interface ErrorPageProps {
  error?: Error;
  status?: ResultStatusType;
  title?: string;
  withExtra?: boolean;
}

export const ErrorPage: FC<ErrorPageProps> = ({
  error,
  title,
  status,
  withExtra = true,
}) => {
  const handleReloadPage = (): void => {
    window.location.reload();
  };
  return (
    <PageContainer containerKey="500">
      <AppResult
        title={title || 'Что то пошло не так, попробуйте обновить страницу'}
        status={status || 500}
        extra={
          withExtra && (
            <div className={styles.errorSend}>
              <Button type="primary" onClick={handleReloadPage}>
                Обновить страницу
              </Button>
              <Button
                role="link"
                href={composeMailto(
                  error ? error.toString() : 'Произошла ошибка при загруке',
                )}
              >
                Отправить ошибку разработчику
              </Button>
            </div>
          )
        }
      />
    </PageContainer>
  );
};
