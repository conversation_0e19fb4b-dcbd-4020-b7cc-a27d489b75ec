import { useCallback } from 'react';
import { apiUrls, postForBlobFile } from 'shared/api';
import {
  asyncDownloadFile,
  lazyDownloadFile,
  generateUrlWithQueryParams,
} from 'shared/lib';
import { DownloadModalProps, InputValues } from '../..';
import { reportErrorMessages } from '../../config';

// Параметры отложенной загрузки отчетов
export const FILE_CHECK_DELAY = 10000;
export const MAX_FILE_CHECK_ATTEMPTS = 360;

export const useDownloadReport = (
  url: Endpoint,
  isLazyReport: boolean,
  onOutputOptions: DownloadModalProps['onOutputOptions'],
  reportControlDTO: ReportControlDTO,
  additionalRequestParams?: Record<string, string>,
): ((inputValues: InputValues) => void) =>
  useCallback(
    (inputValues: InputValues) =>
      asyncDownloadFile(
        onOutputOptions && typeof onOutputOptions.customCallback === 'function'
          ? onOutputOptions.customCallback(inputValues)
          : isLazyReport
          ? lazyDownloadFile<ReportControlDTO>(
              reportControlDTO,
              generateUrlWithQueryParams(
                `${apiUrls.download.report(url)}_file_id`,
                {
                  format: inputValues.reportType,
                  type: inputValues.format,
                  highlight: String(inputValues.isHighlighted),
                  ...additionalRequestParams,
                },
              ),
              `${apiUrls.download.report(url)}_file_state`,
              `${apiUrls.download.report(url)}_file_download`,
              MAX_FILE_CHECK_ATTEMPTS,
              FILE_CHECK_DELAY,
            )
          : postForBlobFile<ReportControlDTO>(
              generateUrlWithQueryParams(apiUrls.download.report(url), {
                format: inputValues.reportType,
                type: inputValues.format,
                highlight: String(inputValues.isHighlighted),
                ...additionalRequestParams,
              }),
              reportControlDTO,
            ),

        onOutputOptions?.customErrorMessages || reportErrorMessages,
      ),
    [
      additionalRequestParams,
      isLazyReport,
      onOutputOptions,
      reportControlDTO,
      url,
    ],
  );
