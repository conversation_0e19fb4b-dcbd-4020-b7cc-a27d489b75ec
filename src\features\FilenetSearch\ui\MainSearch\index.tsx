import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Select,
  Tooltip,
} from 'antd';
import classNames from 'classnames';
import moment, { Moment } from 'moment';
import { FC } from 'react';
import { useEffectOnce } from 'react-use';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { SearchFormProps } from '../..';
import { enums } from '../../config';
import { reducers, selectors } from '../../store';

import styles from './styles.module.scss';

const PERIOD_KEY = 'verifyPeriod';
const PERIOD_FROM_KEY = `verifyPeriodFrom`;
const PERIOD_TO_KEY = `verifyPeriodTo`;
const CB_GROUP_KEY = 'include';
const CB_GROUP_MAIN_FILE_KEY = 'includeMainFile';
const CB_GROUP_ANNEX_KEY = 'includeAnnex';

export const MainSearch: FC<SearchFormProps> = ({
  onSubmit,
  onClear,
  isPending,
}) => {
  const [form] = Form.useForm();
  const { setMainSearch } = useCreateSliceActions(reducers.slice.actions);

  const formValues = useAppSelector(selectors.mainSearchSelector);

  const isDisabled = (value: unknown): boolean => {
    if (value === 0) return false;

    return !value;
  };

  /** Загрузка значений из стейта */
  useEffectOnce(() => {
    const isHasPeriodFrom = Boolean(
      formValues[PERIOD_FROM_KEY] && formValues[PERIOD_FROM_KEY] !== '',
    );
    const isHasPeriodTo = Boolean(
      formValues[PERIOD_TO_KEY] && formValues[PERIOD_TO_KEY] !== '',
    );

    form.setFieldsValue({
      ...formValues,
      ...(isHasPeriodFrom &&
        isHasPeriodTo && {
          [PERIOD_KEY]: [
            moment(formValues[PERIOD_FROM_KEY], DEFAULT_DATE_VARIANT),
            moment(formValues[PERIOD_TO_KEY], DEFAULT_DATE_VARIANT),
          ],
        }),
      [CB_GROUP_KEY]: [
        formValues[CB_GROUP_MAIN_FILE_KEY] ? CB_GROUP_MAIN_FILE_KEY : '',
        formValues[CB_GROUP_ANNEX_KEY] ? CB_GROUP_ANNEX_KEY : '',
      ],
    });
  });

  const isMainFileDisabled = isDisabled(form.getFieldValue('id'));

  return (
    <Form
      name="main-search"
      className={styles.form}
      onFinish={onSubmit}
      form={form}
      onValuesChange={(_, fields) => {
        const copy = { ...fields };

        delete copy[PERIOD_KEY];
        delete copy[CB_GROUP_KEY];

        if (fields[PERIOD_KEY]) {
          copy[PERIOD_FROM_KEY] = (fields[PERIOD_KEY][0] as Moment).format(
            DEFAULT_DATE_VARIANT,
          );
          copy[PERIOD_TO_KEY] = (fields[PERIOD_KEY][1] as Moment).format(
            DEFAULT_DATE_VARIANT,
          );
        }

        if (isDisabled(fields.id)) {
          // Если чекбокс CB_GROUP_MAIN_FILE_KEY должен быть отключен, снимаем его выбор
          const currentCheckboxValues = fields[CB_GROUP_KEY] || [];
          const updatedCheckboxValues = currentCheckboxValues.filter(
            (value: string) => value !== CB_GROUP_MAIN_FILE_KEY,
          );

          // Записываем обновленные чекбоксы
          form.setFieldsValue({ [CB_GROUP_KEY]: updatedCheckboxValues });
          copy.includeAnnex = updatedCheckboxValues.includes('includeAnnex');
          copy.includeMainFile = false;
        } else if (fields[CB_GROUP_KEY]) {
          const set = new Set<string>(fields[CB_GROUP_KEY]);
          copy[CB_GROUP_MAIN_FILE_KEY] = set.has(CB_GROUP_MAIN_FILE_KEY);
          copy[CB_GROUP_ANNEX_KEY] = set.has(CB_GROUP_ANNEX_KEY);
        }

        setMainSearch(copy);
      }}
      autoComplete="off"
      labelWrap
      labelAlign="left"
    >
      <div className={classNames(styles.row, styles.row_antFix)}>
        <Form.Item label="Имя файла" name="title" colon={false}>
          <Input />
        </Form.Item>

        <Form.Item label="Путь" name="contentPath" colon={false}>
          <Input />
        </Form.Item>

        <Form.Item
          label="Идентификатор файла или досье проверки"
          name="id"
          colon={false}
        >
          <InputNumber className={styles.input} min={0} />
        </Form.Item>

        <Form.Item
          label="Период проведения проверок"
          name={PERIOD_KEY}
          colon={false}
        >
          <DatePicker.RangePicker
            format={DATE_VARIANTS_LIST}
            className={styles.input}
          />
        </Form.Item>

        <Form.Item name="sorting" label="Сортировать по" colon={false}>
          <Select>
            <Select.Option className={styles.selectDefaultValue} value="">
              Выбрать
            </Select.Option>
            <Select.Option value={enums.Sorting.BY_PUBLISH_DATE}>
              Дате публикации
            </Select.Option>
            <Select.Option value={enums.Sorting.BY_RANKING}>
              Релевантности
            </Select.Option>
            <Select.Option value={enums.Sorting.BY_NAME}>
              Заголовку
            </Select.Option>
          </Select>
        </Form.Item>
      </div>

      <div className={styles.row}>
        <Form.Item name={CB_GROUP_KEY}>
          <Checkbox.Group>
            <Tooltip
              title={
                isMainFileDisabled
                  ? 'Не указан идентификатор файла или досье проверки'
                  : ''
              }
            >
              <Checkbox
                value={CB_GROUP_MAIN_FILE_KEY}
                disabled={isMainFileDisabled}
              >
                Отображать головной файл
              </Checkbox>
            </Tooltip>
            <Checkbox value={CB_GROUP_ANNEX_KEY}>
              Отображать приложения
            </Checkbox>
          </Checkbox.Group>
        </Form.Item>

        <div className={styles.buttonsContainer}>
          <Button type="primary" htmlType="submit" loading={isPending}>
            Найти
          </Button>
          <Button
            loading={isPending}
            type="primary"
            danger
            ghost
            onClick={() => {
              onClear();
              form.resetFields([
                'contentPath',
                'sorting',
                'title',
                'id',
                PERIOD_KEY,
                CB_GROUP_KEY,
              ]);
            }}
          >
            Очистить
          </Button>
        </div>
      </div>
    </Form>
  );
};
