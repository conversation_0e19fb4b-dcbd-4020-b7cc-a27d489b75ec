import { useCallback } from 'react';
import { apiUrls, emptyBaseUrlInstance } from 'shared/api';
import { IS_ZT_BUILD } from 'shared/config';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAppSelector } from 'shared/model';

import { parseBlitzUrlToLogout } from '../lib';
import { selectors } from '.';

export const useBlitzLogout = (): Callback => {
  const blitzUrl = useAppSelector(selectors.blitzUrlSelector);
  const { isPending } = useAppSelector(selectors.blitzApiSelector);

  return useCallback(() => {
    localStorage.removeItem('userName');
    if (!isPending && IS_ZT_BUILD) {
      emptyBaseUrlInstance.post(apiUrls.blitz.logout).finally(() => {
        (window as Window).location = generateUrlWithQueryParams(
          parseBlitzUrlToLogout(blitzUrl),
          {
            redirect: window.location.origin,
          },
        );
      });
    }
  }, [blitzUrl, isPending]);
};
