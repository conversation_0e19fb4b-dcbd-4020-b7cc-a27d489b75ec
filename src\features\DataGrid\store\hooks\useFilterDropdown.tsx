import { InputRef } from 'antd';
import { FilterDropdownProps } from 'antd/lib/table/interface';
import { FC, Ref, useCallback } from 'react';
import { AppFilterDropDownProps, TableColumnData } from '../..';

type RenderFilterDropdown = (
  dataIndex: string,
  searchInput: Ref<InputRef>,
  title?: TableColumnData['title'],
) => (props: FilterDropdownProps) => JSX.Element;

export const useFilterDropdown = (
  FilterNode: FC<AppFilterDropDownProps>,
): RenderFilterDropdown => {
  const renderFilter = useCallback<RenderFilterDropdown>(
    (
        dataIndex: string,
        searchInput: Ref<InputRef>,
        title?: TableColumnData['title'],
      ) =>
      ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: FilterDropdownProps): JSX.Element =>
        (
          <FilterNode
            setSelectedKeys={setSelectedKeys}
            selectedKeys={selectedKeys}
            confirm={confirm}
            searchInput={searchInput}
            title={title}
            clearFilters={clearFilters}
          />
        ),
    [], // eslint-disable-line
  );

  return renderFilter;
};
