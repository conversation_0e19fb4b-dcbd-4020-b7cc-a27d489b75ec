import { EventDataNode } from 'antd/lib/tree';
import { MutableRefObject, ReactNode, RefObject } from 'react';
import { PermissionsInitial } from 'entities/Permissions';
import { RequestTrigger, TreeSelect } from 'shared/model';

export type WorkGroupEPCProps = Omit<EPCInnerProps, 'height'> & {
  handleClose: Callback;
  isOpened: boolean;
};

export type TreeChecked = {
  keys: Key[];
  nodes: TreeElement[];
  selectedDirs: TreeElement[];
  selectedFiles: TreeElement[];
};

export type UseTreeTypes = {
  flatTree: TreeElement[];
  tree: {
    data: TreeElement[];
    error: AppError;
    handleResetTree: Callback;
    handleTree: (payload: TreeElement[]) => void;
    isPending: boolean;
    linkedData: LinkedData;
  };
  treeActions: {
    handleMove: (body: MoveDNDBody) => Promise<string>;
    refetchNode: (node: TreeElement) => Promise<void>;
    refetchTree: Callback;
  };
  treeArrow: {
    arrowDown: Callback;
    arrowUp: Callback;
    currentIndex: number;
    foundKey: Key;
    tagged: TreeElement[];
  };
  treeCheck: {
    checked: TreeChecked;
    handleCheck: (
      checked: Key[] | { checked: Key[]; halfChecked: Key[] },
      info: { checked: boolean; node: TreeElement },
    ) => void;
    resetCheck: Callback;
  };
  treeDrag: {
    isDrag: boolean;
    setIsDrag: React.Dispatch<React.SetStateAction<boolean>>;
  };
  treeExpand: {
    autoExpandParent: boolean;
    expandedKeys: Key[];
    handleExpand: (keys: Key[]) => void;
  };
  treeLink: {
    foundLink: React.Key;
    getLinked: RequestTrigger<TreeElement[]>;
    handleLinkClick: (isDirectory: boolean, itemId: string) => void;
    handleUpdateLinked: (payload: LinkedData) => void;
    isLinkedReqEnd: boolean;
    resetFoundLinkKey: Callback;
    setIsLinkedReqEnd: React.Dispatch<React.SetStateAction<boolean>>;
  };
  treeLoad: {
    loadedKeys: Key[];
    loadedKeysSet: Set<React.Key>;
    onLoadData: (treeNode: EventDataNode<TreeElement>) => Promise<void>;
    setLoadedKeys: React.Dispatch<React.SetStateAction<React.Key[]>>;
  };
  treeRefs: {
    treeRef: {
      <T>(initialValue: T): MutableRefObject<T>;
      <T>(initialValue: T | null): RefObject<T>;
      <T = undefined>(): MutableRefObject<T | undefined>;
    };
  };
  treeSearch: {
    handleSearch: (body: object) => Promise<void>;
    isReqEnd: boolean;
    setIsReqEnd: React.Dispatch<React.SetStateAction<boolean>>;
  };
  treeSelect: {
    handleReset: Callback;
    handleSelect: (payload: {
      selectedKeys: Key[];
      selectedNode: TreeElement;
    }) => void;
    selectedValues: TreeSelect;
  };
};

export interface EPCInnerProps {
  cabinetId: string;
  height: number;
  isFullFilesAccess: boolean;
  permissions: PermissionsInitial;
  isFullSize?: boolean;
}

export interface WorkGroupEPCInitial {
  epc: {
    buttons: ApiDefaultKeys;
    linkedData: LinkedData;
    treeData: EPCTree;
  };
}

export type EditLinked = Pick<
  TreeElement,
  'itemId' | 'title' | 'isDirectory'
>[];

export interface LinkedData {
  directoryId: string;
  editLinked: EditLinked;
  title: ReactNode;
  watchLinked: EditLinked;
}

interface EPCTree extends ApiDefaultKeys {
  tree: TreeElement[];
}

export interface TreeMoveBody {
  itemId: string;
  parentId: string;
  position: number;
}

export type TogglePopup = (
  name: keyof typeof import('widgets/WorkGroupEPC').WorkGroupEPCConfig.popupsInitial,
) => void;

export type AdditionalFields = Record<
  'archive' | 'includeSigns' | 'unArchive',
  boolean
>;

export type DownloadSettingsFields = {
  key: keyof AdditionalFields;
  title: Title;
};

export interface DownloadBody {
  archive: boolean;
  directoryIds: Key[];
  extract: boolean;
  fileIds: Key[];
  includeSigns: boolean;
}

export interface EPCDownloadModalInnerProps {
  cabinetId: string;
  handleClose: Callback;
  loadedKeys: Key[];
  resetCheckboxes: Callback;
  selectedDirs: TreeElement[];
  selectedFiles: TreeElement[];
  setSettings: (values: AdditionalFields) => void;
  settings: AdditionalFields;
}

export type EPCDownloadModalProps = EPCDownloadModalInnerProps & {
  isOpened: boolean;
};

export type EpcPermissions = Record<
  | 'canViewFiles'
  | 'canDownloadFiles'
  | 'canDeleteFiles'
  | 'canEdit'
  | 'canEditEPCStructure'
  | 'canCopyFiles',
  boolean
>;

export type FilesVisibility = (
  fileIds: string[],
  directoryIds: string[],
  isCabinetOnly: boolean,
) => Promise<void>;

export type SaveFile = (
  type: 'copyFiles' | 'copyFilesWithCatalogs',
  body: { flat: boolean; items: Partial<TreeElement>[] },
) => Promise<void>;

export type SaveFilesResponse = string;
