import { useMemo, useState } from 'react';
import { InputValues } from 'features/DownloadModal/types';

export const useDownloadInputs = (): [InputValues, typeof inputHandlers] => {
  const [inputValues, setInputValues] = useState<InputValues>({
    format: 1,
    isHighlighted: false,
    reportType: 'xlsx',
  });

  const inputHandlers = useMemo(
    () => ({
      setReportType: (reportType: InputValues['reportType']) => {
        setInputValues((prev) => ({ ...prev, reportType }));
      },
      setHighlight: (isHighlighted: InputValues['isHighlighted']) => {
        setInputValues((prev) => ({ ...prev, isHighlighted }));
      },
      setFormat: (format: InputValues['format']) => {
        setInputValues((prev) => ({ ...prev, format }));
      },
    }),
    [],
  );

  return [inputValues, inputHandlers];
};
