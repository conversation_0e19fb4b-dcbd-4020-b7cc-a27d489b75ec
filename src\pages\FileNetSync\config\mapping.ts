import { pluralAdjectiveForms, pluralNounForms } from '../lib';
import { enums } from '../store';

/** Маппинг слов по времени */
export const typesMapping: Record<
  enums.PeriodType,
  Parameters<typeof pluralNounForms>[0]
> = {
  [enums.PeriodType.DAYS]: { one: 'День', twoToFour: 'Дня', many: 'Дней' },
  [enums.PeriodType.HOURS]: { one: 'Час', twoToFour: 'Часа', many: 'Часов' },
  [enums.PeriodType.MINUTES]: {
    one: 'Минуту',
    twoToFour: 'Минуты',
    many: 'Минут',
  },
};

/** Маппинг слов по дате */
export const labelMapping: Record<
  enums.PeriodType,
  Parameters<typeof pluralAdjectiveForms>[0]
> = {
  [enums.PeriodType.DAYS]: { one: 'каждый', many: 'каждые' },
  [enums.PeriodType.HOURS]: { one: 'каждый', many: 'каждые' },
  [enums.PeriodType.MINUTES]: {
    one: 'каждую',
    many: 'каждые',
  },
};
