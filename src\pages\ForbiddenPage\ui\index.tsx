import { Button } from 'antd';
import type { FC } from 'react';

import { useRedirectBack } from 'shared/model';

import { AppResult } from 'shared/ui/AppResult';
import { PageContainer } from 'shared/ui/PageContainer';

export const ForbiddenPage: FC = () => {
  const handleRedirect = useRedirectBack();

  return (
    <PageContainer containerKey="403">
      <AppResult
        status="403"
        title="К сожалению у вас недостаточно прав для просмотра этой страницы"
        extra={<Button onClick={handleRedirect}>Вернуться назад</Button>}
      />
    </PageContainer>
  );
};
