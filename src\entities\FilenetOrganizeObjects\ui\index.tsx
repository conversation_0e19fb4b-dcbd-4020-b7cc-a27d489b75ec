import { Divider, Typography } from 'antd';
import { memo } from 'react';
import type { FC } from 'react';
// TODO: убрать после переноса датагрида в ui либу
// eslint-disable-next-line boundaries/element-types
import { DataGrid } from 'features/DataGrid';

import { constants } from '../config';

import styles from './styles.module.scss';

export const FilenetOrganizeObjects: FC<{
  isFullSize: boolean;
  rows: [TableColumnsAndRows['rows'], TableColumnsAndRows['rows']];
}> = memo(({ isFullSize, rows }) => (
  <div>
    <Divider>Организационные объекты</Divider>

    {rows.map((row, index) => (
      // Не принципиально, массив не меняется
      // eslint-disable-next-line react/no-array-index-key
      <div key={index} className={styles.tables}>
        <Typography.Title level={5}>{constants.titles[index]}</Typography.Title>
        <DataGrid
          columns={constants.columns}
          rows={row}
          tableAdditionProps={{
            size: isFullSize ? 'middle' : 'small',
            bordered: true,
            scroll: { x: '100%' },
            pagination: {
              size: 'small',
              position: ['bottomCenter'],
              pageSize: 3,
              simple: true,
              hideOnSinglePage: true,
            },
          }}
        />
      </div>
    ))}
  </div>
));
