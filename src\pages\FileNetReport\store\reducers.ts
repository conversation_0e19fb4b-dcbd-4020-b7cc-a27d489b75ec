import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import moment from 'moment';

import { OrgStructuresRows } from 'widgets/OrganizationalStructures';
import { TableRowData } from 'features/DataGrid';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import { FNRInitialState } from '..';
import {
  getMaterialIdsThunk,
  postUserFormThunk,
  postWorkFormThunk,
} from './actions';
import { ActionTypes, Detailing, ReportFormVariant, UserStatus } from './enums';

const sameInputValues = {
  error: null,
  isPending: false,
  periodFrom: moment().subtract(7, 'days').format(DEFAULT_DATE_VARIANT),
  periodTo: moment().subtract().format(DEFAULT_DATE_VARIANT),
};

const initialState: FNRInitialState = {
  isUsersModalOpen: false,
  isWorkModalOpen: false,
  reportFormVariant: ReportFormVariant.HTML,
  userModalApi: {
    ...sameInputValues,
    userStatus: UserStatus.ALL,
  },
  workModalApi: {
    ...sameInputValues,
    detailing: Detailing.FULL,
    actionTypes: Object.values(ActionTypes).map(
      (value) => value as ActionTypes,
    ),
  },
  /* Степпер идентификатора */
  materialIdenticator: {
    isPending: false,
    error: null,
    items: [],
    ids: [],
  },
  /* Степпер типов файлов */
  fileTypes: {
    isOpened: false,
    selectedKeys: [],
    selectedRows: [],
  },
  /* Степпер орг структур */
  orgStructures: {
    isOpened: false,
    ids: [],
    ko: [],
    so: [],
    gibr: [],
    ts: [],
  },
};

export const slice = createSlice({
  name: 'fileNetReport',
  initialState,
  reducers: {
    openUsersModal: (state) => {
      state.isUsersModalOpen = true;
    },
    closeUsersModal: (state) => {
      state.isUsersModalOpen = false;
    },
    openWorkModal: (state) => {
      state.isWorkModalOpen = true;
    },
    closeWorkModal: (state) => {
      state.isWorkModalOpen = false;
    },
    setReportFormVariant: (
      state,
      { payload }: PayloadAction<ReportFormVariant>,
    ) => {
      state.reportFormVariant = payload;
    },
    setUserModalPeriod: (
      state,
      { payload: [periodFrom, periodTo] }: PayloadAction<[string, string]>,
    ) => {
      state.userModalApi.periodFrom = periodFrom;
      state.userModalApi.periodTo = periodTo;
    },
    setUserStatus: (state, { payload }: PayloadAction<UserStatus>) => {
      state.userModalApi.userStatus = payload;
    },
    clearUserModal: (state) => {
      state.userModalApi.userStatus = initialState.userModalApi.userStatus;
      state.userModalApi.periodFrom = initialState.userModalApi.periodFrom;
      state.userModalApi.periodTo = initialState.userModalApi.periodTo;
      state.reportFormVariant = initialState.reportFormVariant;
    },

    setWorkModalPeriod: (
      state,
      { payload: [periodFrom, periodTo] }: PayloadAction<[string, string]>,
    ) => {
      state.workModalApi.periodFrom = periodFrom;
      state.workModalApi.periodTo = periodTo;
    },
    setDetailingStatus: (state, { payload }: PayloadAction<Detailing>) => {
      state.workModalApi.detailing = payload;
    },
    setActionTypes: (state, { payload }: PayloadAction<ActionTypes[]>) => {
      state.workModalApi.actionTypes = payload;
    },
    clearWorkModal: (state) => {
      state.workModalApi.detailing = initialState.workModalApi.detailing;
      state.workModalApi.periodFrom = initialState.workModalApi.periodFrom;
      state.workModalApi.periodTo = initialState.workModalApi.periodTo;
      state.workModalApi.actionTypes = initialState.workModalApi.actionTypes;
      state.reportFormVariant = initialState.reportFormVariant;
      state.fileTypes.selectedKeys = [];
      state.fileTypes.selectedRows = [];
      state.materialIdenticator.ids = initialState.materialIdenticator.ids;
      state.materialIdenticator.items = initialState.materialIdenticator.items;
      state.materialIdenticator.error = initialState.materialIdenticator.error;
      state.orgStructures.ids = initialState.orgStructures.ids;
      state.orgStructures.gibr = initialState.orgStructures.gibr;
      state.orgStructures.ko = initialState.orgStructures.ko;
      state.orgStructures.ts = initialState.orgStructures.ts;
      state.orgStructures.so = initialState.orgStructures.so;
    },
    deleteOneIdenticator: (state, { payload }: PayloadAction<number>) => {
      state.materialIdenticator.ids = state.materialIdenticator.ids.filter(
        (id) => id !== payload,
      );
      state.materialIdenticator.items = state.materialIdenticator.items.filter(
        ({ id }) => id !== payload,
      );
    },
    openFileTypesModal: (state) => {
      state.fileTypes.isOpened = true;
    },
    closeFileTypesModal: (state) => {
      state.fileTypes.isOpened = false;
    },
    addSelectedFileTypes: (
      state,
      { payload }: PayloadAction<TableRowData[]>,
    ) => {
      state.fileTypes.selectedRows.push(...payload);
      state.fileTypes.selectedKeys.push(...payload.map((row) => row.key));
    },
    deleteSelectedFileTypes: (
      state,
      { payload }: PayloadAction<TableRowData[]>,
    ) => {
      const deletedKeys = new Set(payload.map(({ key }) => key));
      const newSelectedRows = state.fileTypes.selectedRows.filter(
        (item) => !deletedKeys.has(item.key),
      );

      state.fileTypes.selectedRows = newSelectedRows;
      state.fileTypes.selectedKeys = newSelectedRows.map((row) => row.key);
    },

    openOrgStructure: (state) => {
      state.orgStructures.isOpened = true;
    },
    closeOrgStructure: (state) => {
      state.orgStructures.isOpened = false;
    },
    addOrgStructuresRows: (
      state,
      {
        payload: { orgType, rows },
      }: PayloadAction<{
        orgType: keyof OrgStructuresRows;
        rows: TableRowData[];
      }>,
    ) => {
      const rowIdsSet = new Set(state.orgStructures.ids);

      state.orgStructures[orgType].push(
        ...rows.filter((row) => !rowIdsSet.has(row.id)),
      );

      rows.forEach((row) => rowIdsSet.add(row.id));
      state.orgStructures.ids = [...rowIdsSet];
    },
    deleteOrgStructuresRow: (
      state,
      {
        payload: { orgType, ids },
      }: PayloadAction<{
        ids: number[];
        orgType: keyof OrgStructuresRows;
      }>,
    ) => {
      const idsSet = new Set(ids);
      state.orgStructures.ids = state.orgStructures.ids.filter(
        (id) => !idsSet.has(id),
      );

      state.orgStructures[orgType] = state.orgStructures[orgType].filter(
        (row) => !idsSet.has(row.id),
      );
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getMaterialIdsThunk.pending, (state) => {
      state.materialIdenticator.isPending = true;
      state.materialIdenticator.error = null;
    });

    builder.addCase(getMaterialIdsThunk.fulfilled, (state, { payload }) => {
      state.materialIdenticator.isPending = false;

      if (!state.materialIdenticator.ids.includes(payload.id)) {
        state.materialIdenticator.ids.push(payload.id);
        state.materialIdenticator.items.push(payload);
      }
    });

    builder.addCase(getMaterialIdsThunk.rejected, (state, action) => {
      state.materialIdenticator.isPending = false;
      state.materialIdenticator.error = action.error;
    });

    builder.addCase(postUserFormThunk.pending, (state) => {
      state.userModalApi.isPending = true;
      state.userModalApi.error = null;
    });

    builder.addCase(postUserFormThunk.fulfilled, (state) => {
      state.userModalApi.isPending = false;
      state.isUsersModalOpen = false;
    });

    builder.addCase(postUserFormThunk.rejected, (state, action) => {
      state.userModalApi.isPending = false;
      state.userModalApi.error = action.error;
    });

    builder.addCase(postWorkFormThunk.pending, (state) => {
      state.workModalApi.isPending = true;
      state.workModalApi.error = null;
    });

    builder.addCase(postWorkFormThunk.fulfilled, (state) => {
      state.workModalApi.isPending = false;
      state.isWorkModalOpen = false;
    });

    builder.addCase(postWorkFormThunk.rejected, (state, action) => {
      state.workModalApi.isPending = false;
      state.workModalApi.error = action.error;
    });
  },
});
