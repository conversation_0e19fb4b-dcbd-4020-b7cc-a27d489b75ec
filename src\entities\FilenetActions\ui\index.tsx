import {
  CopyOutlined,
  DownloadOutlined,
  SelectOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { Button, Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import { apiUrls } from 'shared/api';
import { FilenetActionsProps } from '..';

import { enums } from '../config';
import { hooks } from '../store';

import styles from './styles.module.scss';

export const FilenetActions: FC<FilenetActionsProps> = ({
  permissions,
  cardId,
  attachment,
  hideNotEnoughRights,
  size = 'middle',
}) => {
  const { onCopy, onView, onDownload } = hooks.useActions(cardId, attachment);

  const isNotEnoughRights = !hideNotEnoughRights && permissions.length === 0;
  const hasViewPermission = permissions.includes(enums.Permissions.VIEW);
  const hasDownloadPermission = permissions.includes(
    enums.Permissions.DOWNLOAD,
  );

  if (isNotEnoughRights) {
    return (
      <Typography.Text code>
        Недостаточно прав для отображения действий
      </Typography.Text>
    );
  }

  return (
    <div
      className={classNames(
        styles.row,
        size === 'small' ? styles.smallGap : styles.normalGap,
      )}
    >
      {hasViewPermission && (
        <Tooltip title="Открыть карточку в новой вкладке">
          <Button
            size={size}
            target="_blank"
            type="dashed"
            href={`${apiUrls.fileNet.fileCard(cardId)}`}
            icon={<SelectOutlined />}
          />
        </Tooltip>
      )}

      {[
        {
          key: 'view',
          title: 'Открыть на просмотр',
          onClick: onView,
          Icon: EyeOutlined,
          isNeedRender: hasViewPermission && attachment,
        },
        {
          key: 'download',
          title: 'Скачать',
          onClick: onDownload,
          Icon: DownloadOutlined,
          isNeedRender: hasDownloadPermission && attachment,
        },
        {
          key: 'copy',
          title: 'Скопировать ссылку',
          onClick: onCopy,
          Icon: CopyOutlined,
          isNeedRender: hasViewPermission,
        },
      ].map(
        ({ Icon, ...button }) =>
          button.isNeedRender && (
            <Tooltip key={button.key} title={button.title}>
              <Button
                size={size}
                type="dashed"
                icon={<Icon />}
                onClick={button.onClick}
              />
            </Tooltip>
          ),
      )}
    </div>
  );
};
