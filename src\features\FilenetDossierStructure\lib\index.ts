import { ParsedDossierStructureTree } from 'entities/FilenetAttachmentsTree';

export const setChildrenToTree = (
  list: ParsedDossierStructureTree[],
  key: Key,
  children: ParsedDossierStructureTree[],
): ParsedDossierStructureTree[] =>
  list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children,
      };
    }

    if (node.children) {
      return {
        ...node,
        children: setChildrenToTree(node.children, key, children),
      };
    }

    return node;
  });
