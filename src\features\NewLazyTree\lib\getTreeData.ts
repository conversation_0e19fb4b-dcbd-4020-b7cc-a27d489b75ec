import { TreeState } from '../types';

/**
 * Функция для получения полного дерева в виде вложенной структуры.
 *
 * @returns Массив корневых узлов с вложенными дочерними узлами
 */

export const getTreeData = (
  entities: TreeState['entities'] | undefined,
  rootId: string | string[] | undefined,
): TreeElement[] => {
  if (!entities || Object.keys(entities!).length === 0 || !rootId) return [];

  // Рекурсивная функция для построения дерева
  const buildTree = (nodeIds: string[]): TreeElement[] =>
    nodeIds
      .map((id) => {
        const node = entities[id]!;
        if (!node) return null;
        const children =
          node.childrenIds && node.childrenIds.length > 0
            ? buildTree(node.childrenIds)
            : [];
        return { ...node, children };
      })
      .filter((node) => !!node);

  // Поддержка как одного корневого узла (строка), так и массива корневых узлов
  const rootIds = Array.isArray(rootId) ? rootId : [rootId];
  return buildTree(rootIds);
};
