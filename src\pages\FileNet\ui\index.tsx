import { FilterOutlined, SettingOutlined } from '@ant-design/icons';
import { Divider } from 'antd';
import { useCallback, type FC } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import type { TableRowData } from 'features/DataGrid';
import { DataGrid, dataGridStore } from 'features/DataGrid';
import {
  FilenetDossierStructure,
  filenetDossierStructureStore,
} from 'features/FilenetDossierStructure';

import { Dossier, FileCard } from 'features/FilenetInfoCards';
import { FilenetSearch, filenetSearchStore } from 'features/FilenetSearch';
import { createTableConfigModal } from 'features/TableConfigModal';

import { indexFilesStore } from 'entities/IndexFilesTable';
import {
  useAppSelector,
  useHandleCloseOpen,
  useCreateSliceActions,
} from 'shared/model';

import { ButtonsContainer, PageContainer } from 'shared/ui';

import {
  reducers as fileNetReducer,
  selectors as fileNetSelectors,
  hooks,
} from '../store';

import { FilenetSelectedFilters } from './FilenetSelectedFilters';
import { FiltersDrawer } from './FiltersDrawer';

import { IndexingInfo } from './IndexingInfo';
import styles from './styles.module.scss';

export const FileNet: FC = () => {
  const endpoint = window.location.pathname.replace('/', '');

  /** Получение файлов рубрик */
  indexFilesStore.hooks.useGetIndexFiles();

  const mainTableSelector = useAppSelector(fileNetSelectors.mainTableSelector);
  const isExtendedSearchEmpty = useAppSelector(
    filenetSearchStore.selectors.isExtendedSearchEmptySelector,
  );
  const isExtendedSearchOpen = useAppSelector(
    filenetSearchStore.selectors.isExtendedSearchOpenSelector,
  );
  const docTypeIdsSelector = useAppSelector(
    fileNetSelectors.docTypeIdsSelector,
  );

  const {
    setDocTypeIds,
    setMainTableSelectedFilter,
    setIsAuditCards,
    handleClearFormData,
  } = useCreateSliceActions(fileNetReducer.slice.actions);

  const [pageParams, { handlePopupClose, handleRadioClick, onRowClick }] =
    hooks.useQueryWithParams({ radioHandler: setIsAuditCards });

  const { handleDossierOpen } = useCreateSliceActions(
    filenetDossierStructureStore.reducers.slice.actions,
  );

  const { getFiles, toggleCardsAndDossier, onPagination } =
    hooks.useFilenetApi();

  const {
    isPending: isPresetPending,
    currentSelectedProfile,
    updatedColumns,
    tableSizes,
    onResize,
    onProfileSet,
  } = dataGridStore.hooks.useTableProfile(
    pageParams.tableType === 'dossier'
      ? mainTableSelector.auditColumns
      : mainTableSelector.fileColumns,
    pageParams.tableType,
  );

  const [isOrgLevelsDrawerOpen, openOrgLevelsDrawer, closeOrgLevelsDrawer] =
    useHandleCloseOpen();

  const handlePaginationChange = useCallback(
    (page: number): void => {
      if (mainTableSelector[pageParams.tableType].rows.length === 0) {
        return;
      }

      onPagination(page);
    },
    [mainTableSelector, onPagination, pageParams.tableType],
  );

  return (
    <PageContainer containerKey="file-net-container">
      <AppHeader title="Файловое хранилище" />

      <ButtonsContainer
        className={styles.buttons}
        buttons={[
          {
            key: 'menubar',
            title: 'Настроить организационные уровни и типы файлов',
            tooltip:
              'Открыть окно настройки организационных уровней и типов файлов',
            role: 'menubar',
            onClick: openOrgLevelsDrawer,
            icon: <FilterOutlined />,
          },
          {
            key: 'settings',
            title: 'Настройка таблицы результатов',
            tooltip: 'Настройки отображения таблицы',
            icon: <SettingOutlined />,
            onClick: () => {
              createTableConfigModal({
                endpoint: `${endpoint}-${pageParams.tableType}`,
                tableSizes,
                columns:
                  pageParams.tableType === 'dossier'
                    ? mainTableSelector.auditColumns
                    : mainTableSelector.fileColumns,
                onProfileSet,
                currentSelectedProfile,
              });
            },
            disabled: mainTableSelector.isPending,
          },
        ]}
      />

      <Divider />

      <FilenetSearch
        onSearch={() => getFiles()}
        isPending={mainTableSelector.isPending}
      />

      <DataGrid
        columns={updatedColumns}
        hideColumnSearch
        hideSorter
        rows={mainTableSelector[pageParams.tableType].rows}
        additionalClassNames={{
          container: styles.table,
          table: styles.tableContent,
          spinner: styles.tableSpinner,
        }}
        resizableProps={{
          isActive: true,
          tableSizes,
          onResizeSave: onResize,
        }}
        additionalComponent={
          <FilenetSelectedFilters
            tableType={pageParams.tableType}
            mainTableSelector={mainTableSelector}
            docTypeIdsSelector={docTypeIdsSelector}
            onRadioChange={(selectedTableType) => {
              handleRadioClick(selectedTableType);
              const rowsAndExtendedSearchNotEmty =
                // Чтобы избежать рефетча на пустой таблицe (еще не производился поиск)
                mainTableSelector[pageParams.tableType].rows?.length > 0 &&
                (!isExtendedSearchEmpty || !isExtendedSearchOpen);

              if (rowsAndExtendedSearchNotEmty) {
                toggleCardsAndDossier(selectedTableType === 'dossier');
              }
            }}
            onClose={(filterType) => {
              setMainTableSelectedFilter({
                type: filterType,
                value: null,
              });
              getFiles();
            }}
            onCloseTypeId={(typeId: TableRowData) => {
              setDocTypeIds(
                docTypeIdsSelector.filter(({ id }) => id !== typeId.id),
              );
              getFiles();
            }}
          />
        }
        tableAdditionHandlers={{
          onRowClick: onRowClick(pageParams.tableType),
        }}
        tableAdditionProps={{
          loading: mainTableSelector.isPending || isPresetPending,
          pagination: {
            showSizeChanger: false,
            disabled: mainTableSelector.isPending,
            onChange: handlePaginationChange,
            position: ['bottomLeft'],
            current:
              mainTableSelector[pageParams.tableType].pagination.currentPage,
            total: mainTableSelector[pageParams.tableType].pagination.total,
          },
          size: 'small',
          scroll: { x: '100%', y: 410 },
          rowClassName: (record: TableRowData) =>
            typeof record.color === 'string' ? styles[record.color] : '',
        }}
      />

      <FileCard
        isOpened={
          pageParams.tableType === 'cards' && pageParams.popupId !== null
        }
        onClose={handlePopupClose}
        popupId={pageParams.popupId as string}
        handleDossierOpen={handleDossierOpen}
      />
      <Dossier
        isOpened={
          pageParams.tableType === 'dossier' && pageParams.popupId !== null
        }
        onClose={handlePopupClose}
        popupId={pageParams.popupId as string}
        handleDossierOpen={handleDossierOpen}
      />

      <FilenetDossierStructure />
      <FiltersDrawer
        isOpened={isOrgLevelsDrawerOpen}
        onClose={closeOrgLevelsDrawer}
        handleRowCheck={(selectedRows) => {
          setDocTypeIds(selectedRows);
          getFiles();
        }}
        handleClearFormData={() => {
          handleClearFormData();
          getFiles();
        }}
        handleRowClick={(row, type) => {
          setMainTableSelectedFilter({ type, value: row });
          getFiles();
        }}
      />
      <IndexingInfo />
    </PageContainer>
  );
};
