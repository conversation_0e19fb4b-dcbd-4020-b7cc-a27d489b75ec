import { BulbTwoTone } from '@ant-design/icons';
import { Divider, notification, Result, Tree } from 'antd';
import { FC, memo, useCallback, useMemo } from 'react';
import {
  WorkGroupEPCStore,
  WorkGroupEPCLib,
  UseTreeTypes,
} from 'widgets/WorkGroupEPC';
import { apiUrls } from 'shared/api';
import {
  deepCopyTree,
  getFilteredFlatTreeByParam,
  treeParserByConfig,
} from 'shared/lib';
import {
  renderTreeTitle,
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { CardsLink } from '../CardsLink';

import styles from './styles.module.scss';

type EditModalProps = {
  handleClose: Callback;
  refetch: Callback;
} & Partial<UseTreeTypes>;

export const EditModal: FC<EditModalProps> = memo(
  ({ handleClose, treeLoad, treeExpand }) => {
    const dispatch = useAppDispatch();
    const { getBodyLinks } = WorkGroupEPCLib;

    const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

    const { handleUpdateLinked, handleResetLinked } = useCreateSliceActions(
      WorkGroupEPCStore.reducers.slice.actions,
    );

    const handleCloseModal = useCallback(() => {
      handleClose();
      handleResetLinked();
    }, []); // eslint-disable-line

    const idKeyMap = useMemo(
      () =>
        getFilteredFlatTreeByParam(epc.treeData.tree, 'key').reduce(
          (acc, node) => {
            acc[node.itemId!] = String(node.key!);
            return acc;
          },
          {} as Record<string, string>,
        ),
      [epc.treeData.tree],
    );

    const checkedKeys = useMemo<Key[]>(
      () => epc.linkedData.editLinked.map((item) => idKeyMap[item.itemId!]),
      [epc.linkedData.editLinked, idKeyMap],
    );

    const treeData = useMemo(
      () =>
        treeParserByConfig(
          deepCopyTree(epc.treeData.tree),
          (node) =>
            ({
              ...node,
              disableCheckbox: node.itemId === epc.linkedData.directoryId,
            } as TreeElement),
        ),
      [epc.treeData.tree, epc.linkedData.directoryId], // eslint-disable-line
    );

    const onCheck = useCallback(
      (_, node: { checked: boolean; node: TreeElement }) => {
        const editLinked = node.checked
          ? [
              ...epc.linkedData.editLinked,
              {
                itemId: node.node.itemId,
                title: node.node.title,
                isDirectory: node.node.isDirectory,
              },
            ]
          : epc.linkedData.editLinked.filter(
              (i) => i.itemId !== node.node.itemId,
            );

        handleUpdateLinked({
          ...epc.linkedData,
          editLinked,
        });
      },
      [epc.linkedData, handleUpdateLinked],
    );

    return (
      <AppPopup
        fullSizeClassName={styles.popupFull}
        isOpened
        className={styles.popup}
        onClose={handleCloseModal}
        title={`Редактирование списка ярлыков для "${epc.linkedData.title}"`}
      >
        <div className={styles.transfer}>
          <div className={styles.transferTree}>
            <Tree
              loadedKeys={treeLoad?.loadedKeys}
              checkable
              defaultExpandAll
              treeData={treeData}
              checkStrictly
              checkedKeys={checkedKeys}
              selectable={false}
              showLine
              titleRender={renderTreeTitle}
              onCheck={onCheck}
              autoExpandParent={treeExpand?.autoExpandParent}
              expandedKeys={treeExpand?.expandedKeys}
              loadData={treeLoad?.onLoadData}
              onExpand={treeExpand?.handleExpand}
            />
          </div>

          <Divider type="vertical" className={styles.divider} />

          <div className={styles.transferList}>
            {epc.linkedData.editLinked.length !== 0 ? (
              <CardsLink
                linked={epc.linkedData.editLinked}
                handleClose={handleCloseModal}
                isDeletable
              />
            ) : (
              <Result
                className={styles.empty}
                title="Выберите каталоги и файлы в дереве"
                icon={<BulbTwoTone twoToneColor="#faad14" />}
              />
            )}
          </div>
        </div>

        <ButtonsContainer
          buttons={[
            {
              title: 'Сохранить',
              key: 'save',
              type: 'primary',
              onClick: async () => {
                const linked = getBodyLinks(epc.linkedData.editLinked);

                await dispatch(
                  WorkGroupEPCStore.actions.postLinkedElementsThunk({
                    url: apiUrls.workGroup.EPC.saveLinkedElements,
                    body: {
                      directoryId: epc.linkedData.directoryId,
                      linkedFiles: linked.files,
                      linkedDirectories: linked.catalogs,
                    },
                  }),
                )
                  .unwrap()
                  .then((data) => {
                    if (data.success) {
                      notification.success({
                        message: 'Связи успешно обновлены',
                      });

                      handleUpdateLinked({
                        ...epc.linkedData,
                        watchLinked: epc.linkedData.editLinked,
                      });
                    } else {
                      notification.error({
                        message: `Ошибка обновления связей`,
                      });
                    }

                    handleClose();
                  });
              },
            },
            {
              title: 'Отмена',
              key: 'cancel',
              danger: true,
              ghost: true,
              onClick: handleCloseModal,
            },
          ]}
        />
      </AppPopup>
    );
  },
);
