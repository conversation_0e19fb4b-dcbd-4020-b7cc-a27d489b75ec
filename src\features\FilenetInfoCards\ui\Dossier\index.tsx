import { Button, DatePicker, Divider, Input, Tabs } from 'antd';
import axios from 'axios';
import classNames from 'classnames';
import moment from 'moment';
import { useMemo } from 'react';
import type { FC } from 'react';
import { useToggle } from 'react-use';
import { FilenetOrganizeObjects } from 'entities/FilenetOrganizeObjects';
import { errorMessages } from 'shared/config';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import { AppPopup, ApiContainer, AppResult, InputRow } from 'shared/ui';
import type { FilenetInfoCardProps, PrivateFilenetInfoCardProps } from '../..';
import { hooks } from '../../store';

import { AssDocTab } from './AssDocTab';
import styles from './styles.module.scss';

const Card: FC<PrivateFilenetInfoCardProps> = ({
  popupId,
  handleDossierOpen,
  isFullSize,
}) => {
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const [rows, { data, error, isPending, isResEnd }] =
    hooks.useGetFilenetCardRows(popupId);
  const [{ tabs, selectedTab }, setSelectedTab, rubricsStatuses] =
    hooks.useDossierTabs(popupId);

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */

  const inputs = useMemo(() => {
    if (isResEnd && data) {
      const list = [
        { label: 'Код проверки', value: data.title },
        { label: 'Досье опубликовано', value: data.uploadDatetime },
        { label: 'Досье обновлено', value: data.modifiedDatetime },
      ].map(({ label, value }) => (
        <InputRow title={label} key={label}>
          <Input
            disabled
            value={value}
            className={styles.input}
            size={isFullSize ? 'middle' : 'small'}
          />
        </InputRow>
      ));

      list.push(
        <InputRow title="Период проверки" key="range-picker">
          <DatePicker.RangePicker
            disabled
            format={DEFAULT_DATE_VARIANT}
            size={isFullSize ? 'middle' : 'small'}
            className={styles.input}
            value={[
              moment(data.auditPeriodFrom, DEFAULT_DATE_VARIANT),
              moment(data.auditPeriodTo, DEFAULT_DATE_VARIANT),
            ]}
            inputReadOnly
          />
        </InputRow>,
      );

      list.push(
        <InputRow title="Досье проверки" key="dossier-finder">
          <Button
            type="primary"
            onClick={() => handleDossierOpen(popupId)}
            size={isFullSize ? 'middle' : 'small'}
          >
            Структура досье проверки
          </Button>
        </InputRow>,
      );

      return list;
    }

    return null;
  }, [isResEnd, data, isFullSize, popupId]); // eslint-disable-line

  return (
    <ApiContainer
      error={error}
      isPending={isPending}
      errorTitle={
        axios.isAxiosError(error)
          ? error.response?.data.message
          : errorMessages.pendingError
      }
      errorStatus={404}
    >
      <div className={classNames(styles.column)}>
        {inputs}

        <FilenetOrganizeObjects isFullSize={isFullSize} rows={rows} />

        <Divider>Прикрепленные файлы</Divider>

        <ApiContainer
          error={rubricsStatuses.error}
          isPending={rubricsStatuses.isPending}
        >
          {tabs === null || tabs.length === 0 || !selectedTab ? (
            <AppResult title="Прикрепленные файлы не найдены" status={404} />
          ) : (
            <Tabs
              type="card"
              onChange={setSelectedTab}
              activeKey={selectedTab}
              items={tabs.map((tabTitle) => ({
                key: tabTitle,
                label: tabTitle,
                children: <AssDocTab popupId={popupId} tabKey={tabTitle} />,
              }))}
            />
          )}
        </ApiContainer>
      </div>
    </ApiContainer>
  );
};

export const Dossier: FC<FilenetInfoCardProps> = ({
  onClose,
  popupId,
  handleDossierOpen,
  isOpened,
}) => {
  const [isFullSize, toggleFullSize] = useToggle(false);

  return (
    <AppPopup
      isOpened={isOpened}
      onClose={onClose}
      key="dossier"
      title="Досье проверки"
      fullSizeClassName={styles.container_fullSize}
      additionalFullSizeHandler={toggleFullSize}
      className={styles.container}
    >
      <Card
        handleDossierOpen={handleDossierOpen}
        popupId={popupId}
        isFullSize={isFullSize}
      />
    </AppPopup>
  );
};
