import { useCallback, useRef, useState } from 'react';
import { apiUrls } from 'shared/api';
import {
  collectNodes<PERSON>ith<PERSON>hildren,
  generateUrlWithQueryParams,
  getFilteredFlatTreeByParam,
} from 'shared/lib';
import { useAppDispatch } from 'shared/model';
import { WorkGroupEPCStore } from '..';

type UseTreeLink = [
  boolean,
  React.Dispatch<React.SetStateAction<boolean>>,
  Key,
  Callback,
  (isDirectory: boolean, itemId: string) => void,
];

const DELAY_TO_SCROLL_MS = 200;

export const useTreeLink = (
  tree: TreeElement[],
  treeRef: typeof useRef,
  expandedKeys: Key[],
  handleExpand: (keys: Key[]) => void,
  handleAutoExpand: Callback,
  loadedKeys: Key[],
  setLoadedKeys: (keys: Key[]) => void,
  cabinetId: string,
): UseTreeLink => {
  const [isLinkedReqEnd, setIsLinkedReqEnd] = useState<boolean>(false);
  const [foundLinkKey, setFoundLinkKey] = useState<Key>('');
  const dispatch = useAppDispatch();

  const resetFoundLinkKey = useCallback(() => setFoundLinkKey(''), []);

  const handleLinkClick = useCallback(
    async (isDirectory: boolean, itemId: string): Promise<void> => {
      const item = getFilteredFlatTreeByParam(tree, 'itemId').find(
        (i) => i.itemId === itemId,
      );

      if (!item) {
        setLoadedKeys([]);

        const payload = await dispatch(
          WorkGroupEPCStore.actions.postSearchThunk({
            url: generateUrlWithQueryParams(
              apiUrls.workGroup.EPC.searchLazyTree,
              {
                cabinetId,
                isCabinet: true,
              },
            ),
            body: {
              ...(isDirectory ? { directoryId: itemId } : { fileId: itemId }),
            },
          }),
        ).unwrap();

        const newLoadedKeys = collectNodesWithChildren(payload.treeData).map(
          (node) => node.key,
        );

        setIsLinkedReqEnd(true);
        setLoadedKeys(newLoadedKeys);
        handleExpand(newLoadedKeys);
        handleAutoExpand();
        setTimeout(() => {
          const scrollKey = getFilteredFlatTreeByParam(
            payload.treeData,
            'itemId',
          ).find((i) => i.itemId === itemId)?.key;

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          treeRef.current.scrollTo({ key: scrollKey });
          setFoundLinkKey(scrollKey || '');
        }, 200);
      } else {
        setIsLinkedReqEnd(true);
        const scrollKey = getFilteredFlatTreeByParam(tree, 'itemId').find(
          (i) => i.itemId === itemId,
        )?.key;

        const parentKey =
          scrollKey !== '0-0'
            ? String(scrollKey).split('-').slice(0, -1).join('-')
            : '0-0';

        if (!expandedKeys.includes(parentKey)) {
          handleExpand([...expandedKeys, parentKey]);
        }

        handleAutoExpand();

        setFoundLinkKey(scrollKey || '');

        if (scrollKey !== '') {
          setTimeout(() => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            treeRef.current.scrollTo({ key: scrollKey });
          }, DELAY_TO_SCROLL_MS);
        }
      }
    },
    [
      cabinetId,
      dispatch,
      expandedKeys,
      handleAutoExpand,
      handleExpand,
      setLoadedKeys,
      tree,
      treeRef,
    ],
  );

  return [
    isLinkedReqEnd,
    setIsLinkedReqEnd,
    foundLinkKey,
    resetFoundLinkKey,
    handleLinkClick,
  ];
};
