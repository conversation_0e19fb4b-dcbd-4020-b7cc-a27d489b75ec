.root {
  position: absolute;
  top: 6px;
  left: 50%;
  padding: 10px 16px 10px 24px;
  transform: translateX(-50%);
  line-height: 1 !important;
  display: flex;
  gap: 16px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 5px;
}

.message {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 13px;
  color: '#595959';
}

.closeButton {
  display: flex;
  align-items: center;
}
