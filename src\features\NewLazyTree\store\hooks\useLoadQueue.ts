import { EventDataNode } from 'antd/lib/tree';
import { useCallback, useRef } from 'react';
import { LoadData } from 'features/NewLazyTree/types';

// Хук для последовательной загрузки веток дерева. Устраняет баги компонента Tree antd
export const useLoadQueue = (handleLoadData: LoadData): LoadData => {
  // Храним последний промис, чтобы дождаться его выполнения
  const lastPromiseRef = useRef<Promise<void>>(Promise.resolve());

  const loadTreeNodeData = useCallback(
    async (node: EventDataNode<TreeElement>): Promise<void> => {
      // Создаём новый промис, который будет ждать завершения предыдущего
      const newPromise = lastPromiseRef.current
        .catch(() => {
          /* Игнорируем ошибки предыдущих запросов (их обработает Redux)*/
        })
        .then(() => handleLoadData(node)); // Запускаем новый запрос только после завершения предыдущего

      // Обновляем ссылку на последний промис
      lastPromiseRef.current = newPromise;

      // Ждём завершения текущего запроса
      await newPromise;
    },
    [handleLoadData],
  );

  return loadTreeNodeData;
};

// Хук для параллельной загрузки данных, но последовательным разрешением промисов (в порядке вызова)
export const useLoadQueueParralell = (handleLoadData: LoadData): LoadData => {
  const loadQueue = useRef<Promise<void>[]>([]);

  const loadTreeNodeData = useCallback(
    async (node: EventDataNode<TreeElement>): Promise<void> => {
      const currentIndex = loadQueue.current.length;
      const loadPromise = handleLoadData(node);

      loadQueue.current.push(loadPromise);

      try {
        if (currentIndex > 0) {
          const currentQueue = loadQueue.current.slice(0, currentIndex);
          await Promise.all(currentQueue);
        }

        await loadPromise;
      } finally {
        loadQueue.current.splice(0, currentIndex + 1);
      }
    },
    [handleLoadData],
  );

  return loadTreeNodeData;
};
