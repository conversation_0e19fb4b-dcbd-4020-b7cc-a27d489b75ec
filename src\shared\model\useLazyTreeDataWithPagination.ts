import { EventDataNode } from 'antd/lib/tree';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  appErrorNotification,
  setChildrenToLazyTree,
  treeParserByConfig,
  updateLazyTreeFileCount,
  createPaginatedTreeUrl,
  hasMoreItems,
} from 'shared/lib';
import { useAxiosRequest, useLoadQueue } from 'shared/model';

// Типы для пагинации (перенесены из widgets для соблюдения архитектуры)
export interface NodePaginationInfo {
  currentPage: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  loadedItemsCount: number;
}

export type TreePaginationState = Map<string, NodePaginationInfo>;
export type LoadMoreCallback = (nodeKey: string) => Promise<void>;

type SetLoadedKeys = React.Dispatch<React.SetStateAction<React.Key[]>>;
type RefetchNode = (
  node: TreeElement,
  isRefetch?: boolean,
) => Promise<TreeElement[]>;

const parserConfig = (node: TreeElement): TreeElement => ({
  ...node,
  disabled: false,
  isDisabled: node.isDisabled,
});

const DEFAULT_PAGE_SIZE = 10;

/**
 * Расширенный хук для ленивой загрузки дерева с поддержкой пагинации
 * Поддерживает функциональность "Load More" для узлов с большим количеством
 * дочерних элементов
 */
export const useLazyTreeDataWithPagination = (
  startedTreeData: TreeElement[],
  endpoint: string,
  cabinetId: string,
  setTree: (tree: TreeElement[]) => void,
  isCabinet: boolean,
  setExpandedKeys?: (keys: Key[] | ((prevKeys: Key[]) => Key[])) => void,
  updateChildren?: (node: TreeElement[]) => TreeElement[],
  defaultPageSize: number = DEFAULT_PAGE_SIZE,
): {
  // Новые методы для пагинации
  loadMoreItems: LoadMoreCallback;
  loadedKeys: Key[];
  loadedKeysSet: Set<React.Key>;
  onLoadData: typeof onLoadData;
  paginationState: TreePaginationState;
  refetchNode: RefetchNode;
  resetPagination: (nodeKey?: string) => void;
  setLoadedKeys: SetLoadedKeys;
} => {
  const [trigger] = useAxiosRequest<TreeElement[]>();
  const treeDataRef = useRef<TreeElement[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<Key[]>([]);
  const [paginationState, setPaginationState] = useState<TreePaginationState>(
    new Map(),
  );

  const loadedKeysSet = useMemo(() => new Set(loadedKeys), [loadedKeys]);

  useEffect(() => {
    treeDataRef.current = treeParserByConfig(startedTreeData, parserConfig);
  }, [startedTreeData]);

  /** Обновляет состояние пагинации для конкретного узла */
  const updateNodePagination = useCallback(
    (nodeKey: string, paginationInfo: Partial<NodePaginationInfo>) => {
      setPaginationState((prev) => {
        const newState = new Map(prev);
        const currentInfo = newState.get(nodeKey) || {
          currentPage: 1,
          hasMore: false,
          isLoadingMore: false,
          loadedItemsCount: 0,
        };
        newState.set(nodeKey, { ...currentInfo, ...paginationInfo });
        return newState;
      });
    },
    [],
  );

  /** Сбрасывает состояние пагинации для узла или всех узлов */
  const resetPagination = useCallback((nodeKey?: string) => {
    if (nodeKey) {
      setPaginationState((prev) => {
        const newState = new Map(prev);
        newState.delete(nodeKey);
        return newState;
      });
    } else {
      setPaginationState(new Map());
    }
  }, []);

  /** Загружает дочерние элементы узла с поддержкой пагинации */
  const loadNodeChildren = useCallback(
    async (
      node: TreeElement,
      page = 1,
      isLoadMore = false,
    ): Promise<TreeElement[]> => {
      const { itemId, key } = node;
      const nodeKey = String(key);

      // Устанавливаем состояние загрузки
      if (isLoadMore) {
        updateNodePagination(nodeKey, { isLoadingMore: true });
      }

      try {
        const url = createPaginatedTreeUrl(
          endpoint,
          {
            itemId,
            pos: String(key),
            cabinetId,
            isCabinet,
          },
          page,
          defaultPageSize,
        );

        const treeLeafs = await trigger(url);

        // Определяем hasMore эвристически на основе количества полученных элементов
        const hasMore = hasMoreItems(treeLeafs.length, defaultPageSize);

        updateNodePagination(nodeKey, {
          currentPage: page,
          hasMore,
          isLoadingMore: false,
          loadedItemsCount: isLoadMore
            ? (paginationState.get(nodeKey)?.loadedItemsCount || 0) +
              treeLeafs.length
            : treeLeafs.length,
        });

        return treeLeafs;
      } catch (error) {
        updateNodePagination(nodeKey, { isLoadingMore: false });
        throw error;
      }
    },
    [
      trigger,
      endpoint,
      cabinetId,
      isCabinet,
      defaultPageSize,
      updateNodePagination,
      paginationState,
    ],
  );

  /** Загружает дополнительные элементы для узла (Load More функциональность) */
  const loadMoreItems: LoadMoreCallback = useCallback(
    async (nodeKey: string) => {
      const node = treeDataRef.current.find((n) => String(n.key) === nodeKey);
      if (!node) return;

      const currentPagination = paginationState.get(nodeKey);
      const nextPage = (currentPagination?.currentPage || 1) + 1;

      const newItems = await loadNodeChildren(node, nextPage, true);

      if (newItems.length > 0) {
        // Добавляем новые элементы к существующим дочерним элементам
        const processedItems = updateChildren
          ? updateChildren(newItems)
          : newItems;
        const safeProcessedItems = Array.isArray(processedItems)
          ? processedItems
          : [];

        treeDataRef.current = setChildrenToLazyTree(
          treeDataRef.current,
          nodeKey,
          treeParserByConfig(safeProcessedItems, parserConfig),
          true, // append mode - добавляем к существующим
        );

        const countedTree = updateLazyTreeFileCount(
          treeDataRef.current,
          loadedKeys,
        );
        setTree(countedTree);
      }
    },
    [loadNodeChildren, updateChildren, loadedKeys, setTree, paginationState],
  );

  /** Основная функция для загрузки/перезагрузки узла */
  const refetchNode = useCallback(
    async (
      { itemId, isDirectory, key, childrenCount }: TreeElement,
      isRefetch = true,
    ): Promise<TreeElement[]> =>
      new Promise<TreeElement[]>((resolve, reject) => {
        if (!isDirectory) {
          resolve([]);
          return;
        }

        const nodeKey = String(key);

        if (isRefetch) {
          // Показываем скелетоны при перезагрузке
          setTree(
            treeParserByConfig(
              treeDataRef.current,
              (node: TreeElement): TreeElement => {
                if (String(node.key).startsWith(`${nodeKey}-`)) {
                  return {
                    ...node,
                    title: '',
                    isSkeleton: true,
                    width: Math.floor(Math.random() * 31) + 60,
                    disabled: true,
                    checked: false,
                    isCallable: false,
                  };
                }
                return node;
              },
            ),
          );

          // Сбрасываем пагинацию при перезагрузке
          resetPagination(nodeKey);
        }

        const newLoadedKeys = [
          ...loadedKeys.filter((k) => !String(k).startsWith(nodeKey)),
          key,
        ];

        loadNodeChildren(
          { itemId, isDirectory, key, childrenCount, title: '' } as TreeElement,
          1,
          false,
        )
          .then((treeLeafs) => {
            const processedLeafs = updateChildren
              ? updateChildren(treeLeafs)
              : treeLeafs || [];
            const safeProcessedLeafs = Array.isArray(processedLeafs)
              ? processedLeafs
              : [];

            treeDataRef.current = setChildrenToLazyTree(
              treeDataRef.current,
              nodeKey,
              treeParserByConfig(safeProcessedLeafs, parserConfig),
            );

            setExpandedKeys?.((prevKeys) => [
              ...new Set([
                ...prevKeys.filter((k) => !String(k).startsWith(nodeKey)),
                key,
              ]),
            ]);

            const countedTree = isRefetch
              ? updateLazyTreeFileCount(treeDataRef.current, newLoadedKeys)
              : treeDataRef.current;
            setTree(countedTree);
            setLoadedKeys(newLoadedKeys);
            resolve(treeLeafs);
          })
          .catch((err) => {
            setTree(setChildrenToLazyTree(treeDataRef.current, nodeKey, []));
            setExpandedKeys?.(loadedKeys);
            if (err.message !== 'canceled') {
              appErrorNotification(
                `Произошла ошибка при загрузке листьев дерева по ключу ${itemId}. Хук useLazyTreeDataWithPagination`,
                err,
              );
            }
            reject(err);
          });
      }),
    [
      loadedKeys,
      setTree,
      updateChildren,
      setExpandedKeys,
      loadNodeChildren,
      resetPagination,
    ],
  );

  const onLoadData = useCallback(
    async (node: EventDataNode<TreeElement>) => {
      if (node.children && node.children.length > 0) {
        return Promise.resolve([]);
      }
      return refetchNode(node, false);
    },
    [refetchNode],
  );

  return {
    loadedKeys,
    loadedKeysSet,
    setLoadedKeys,
    onLoadData: useLoadQueue(onLoadData),
    refetchNode,
    // Новые методы для пагинации
    loadMoreItems,
    paginationState,
    resetPagination,
  };
};
