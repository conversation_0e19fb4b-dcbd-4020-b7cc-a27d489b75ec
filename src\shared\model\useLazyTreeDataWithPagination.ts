import { EventDataNode } from 'antd/lib/tree';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  appErrorNotification,
  generateUrlWithQueryParams,
  setChildrenToLazyTree,
  treeParserByConfig,
  updateLazyTreeFileCount,
} from 'shared/lib';
import { useAxiosRequest, useLoadQueue } from 'shared/model';
import type {
  TreePaginationParams,
  PaginatedTreeResponse,
  NodePaginationInfo,
  TreePaginationState,
  LoadMoreCallback,
} from 'widgets/WorkGroupEPC';

type SetLoadedKeys = React.Dispatch<React.SetStateAction<React.Key[]>>;
type RefetchNode = (node: TreeElement, isRefetch?: boolean) => Promise<TreeElement[]>;

const parserConfig = (node: TreeElement): TreeElement => ({
  ...node,
  disabled: false,
  isDisabled: node.isDisabled,
});

const DEFAULT_SKELETONS_COUNT = 1;
const DEFAULT_PAGE_SIZE = 10;

/**
 * Расширенный хук для ленивой загрузки дерева с поддержкой пагинации
 * Поддерживает функциональность "Load More" для узлов с большим количеством дочерних элементов
 */
export const useLazyTreeDataWithPagination = (
  startedTreeData: TreeElement[],
  endpoint: string,
  cabinetId: string,
  setTree: (tree: TreeElement[]) => void,
  isCabinet: boolean,
  setExpandedKeys?: (keys: Key[] | ((prevKeys: Key[]) => Key[])) => void,
  updateChildren?: (node: TreeElement[]) => TreeElement[],
  defaultPageSize: number = DEFAULT_PAGE_SIZE,
): {
  loadedKeys: Key[];
  loadedKeysSet: Set<React.Key>;
  onLoadData: typeof onLoadData;
  refetchNode: RefetchNode;
  setLoadedKeys: SetLoadedKeys;
  // Новые методы для пагинации
  loadMoreItems: LoadMoreCallback;
  paginationState: TreePaginationState;
  resetPagination: (nodeKey?: string) => void;
} => {
  const [trigger] = useAxiosRequest<PaginatedTreeResponse>();
  const treeDataRef = useRef<TreeElement[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<Key[]>([]);
  const [paginationState, setPaginationState] = useState<TreePaginationState>(new Map());

  const loadedKeysSet = useMemo(() => new Set(loadedKeys), [loadedKeys]);

  useEffect(() => {
    treeDataRef.current = treeParserByConfig(startedTreeData, parserConfig);
  }, [startedTreeData]);

  /**
   * Обновляет состояние пагинации для конкретного узла
   */
  const updateNodePagination = useCallback(
    (nodeKey: string, paginationInfo: Partial<NodePaginationInfo>) => {
      setPaginationState((prev) => {
        const newState = new Map(prev);
        const currentInfo = newState.get(nodeKey) || {
          currentPage: 1,
          totalPages: 1,
          hasMore: false,
          isLoadingMore: false,
          loadedItemsCount: 0,
        };
        newState.set(nodeKey, { ...currentInfo, ...paginationInfo });
        return newState;
      });
    },
    [],
  );

  /**
   * Сбрасывает состояние пагинации для узла или всех узлов
   */
  const resetPagination = useCallback((nodeKey?: string) => {
    if (nodeKey) {
      setPaginationState((prev) => {
        const newState = new Map(prev);
        newState.delete(nodeKey);
        return newState;
      });
    } else {
      setPaginationState(new Map());
    }
  }, []);

  /**
   * Загружает дочерние элементы узла с поддержкой пагинации
   */
  const loadNodeChildren = useCallback(
    async (
      node: TreeElement,
      page: number = 1,
      isLoadMore: boolean = false,
    ): Promise<TreeElement[]> => {
      const { itemId, key } = node;
      const nodeKey = String(key);

      // Устанавливаем состояние загрузки
      if (isLoadMore) {
        updateNodePagination(nodeKey, { isLoadingMore: true });
      }

      try {
        const paginationParams: TreePaginationParams = {
          page,
          size: defaultPageSize,
        };

        const response = await trigger(
          generateUrlWithQueryParams(
            endpoint,
            { 
              itemId, 
              pos: key, 
              cabinetId, 
              isCabinet,
              ...paginationParams,
            },
            { parseAllValues: true },
          ),
        );

        const { treeData: treeLeafs = [], pagination } = response;

        // Обновляем состояние пагинации на основе ответа сервера
        if (pagination) {
          updateNodePagination(nodeKey, {
            currentPage: pagination.currentPage,
            totalPages: pagination.totalPages,
            hasMore: pagination.hasMore,
            isLoadingMore: false,
            loadedItemsCount: pagination.loadedItems,
            totalItemsCount: pagination.totalItems,
          });
        } else {
          // Если сервер не поддерживает пагинацию, определяем hasMore эвристически
          const hasMore = treeLeafs.length >= defaultPageSize;
          updateNodePagination(nodeKey, {
            currentPage: page,
            totalPages: hasMore ? page + 1 : page,
            hasMore,
            isLoadingMore: false,
            loadedItemsCount: isLoadMore 
              ? (paginationState.get(nodeKey)?.loadedItemsCount || 0) + treeLeafs.length
              : treeLeafs.length,
          });
        }

        return treeLeafs;
      } catch (error) {
        updateNodePagination(nodeKey, { isLoadingMore: false });
        throw error;
      }
    },
    [trigger, endpoint, cabinetId, isCabinet, defaultPageSize, updateNodePagination, paginationState],
  );

  /**
   * Загружает дополнительные элементы для узла (Load More функциональность)
   */
  const loadMoreItems: LoadMoreCallback = useCallback(
    async (nodeKey: string, currentPage: number) => {
      const node = treeDataRef.current.find((n) => String(n.key) === nodeKey);
      if (!node) return;

      const nextPage = currentPage + 1;
      const newItems = await loadNodeChildren(node, nextPage, true);

      if (newItems.length > 0) {
        // Добавляем новые элементы к существующим дочерним элементам
        treeDataRef.current = setChildrenToLazyTree(
          treeDataRef.current,
          nodeKey,
          treeParserByConfig(
            updateChildren ? updateChildren(newItems) : newItems,
            parserConfig,
          ),
          true, // append mode - добавляем к существующим
        );

        const countedTree = updateLazyTreeFileCount(treeDataRef.current, loadedKeys);
        setTree(countedTree);
      }
    },
    [loadNodeChildren, updateChildren, loadedKeys, setTree],
  );

  /**
   * Основная функция для загрузки/перезагрузки узла
   */
  const refetchNode = useCallback(
    async (
      { itemId, isDirectory, key, childrenCount }: TreeElement,
      isRefetch = true,
    ): Promise<TreeElement[]> =>
      new Promise<TreeElement[]>((resolve, reject) => {
        if (!isDirectory) {
          resolve([]);
          return;
        }

        const nodeKey = String(key);

        if (isRefetch) {
          // Показываем скелетоны при перезагрузке
          setTree(
            treeParserByConfig(
              treeDataRef.current,
              (node: TreeElement): TreeElement => {
                if (String(node.key).startsWith(`${nodeKey}-`)) {
                  return {
                    ...node,
                    title: '',
                    isSkeleton: true,
                    width: Math.floor(Math.random() * 31) + 60,
                    disabled: true,
                    checked: false,
                    isCallable: false,
                  };
                }
                return node;
              },
            ),
          );

          // Сбрасываем пагинацию при перезагрузке
          resetPagination(nodeKey);
        }

        const newLoadedKeys = [
          ...loadedKeys.filter((k) => !String(k).startsWith(nodeKey)),
          key,
        ];

        loadNodeChildren({ itemId, isDirectory, key, childrenCount }, 1, false)
          .then((treeLeafs) => {
            treeDataRef.current = setChildrenToLazyTree(
              treeDataRef.current,
              nodeKey,
              treeParserByConfig(
                updateChildren ? updateChildren(treeLeafs) : treeLeafs || [],
                parserConfig,
              ),
            );

            setExpandedKeys?.((prevKeys) => [
              ...new Set([
                ...prevKeys.filter((k) => !String(k).startsWith(nodeKey)),
                key,
              ]),
            ]);

            const countedTree = isRefetch
              ? updateLazyTreeFileCount(treeDataRef.current, newLoadedKeys)
              : treeDataRef.current;
            setTree(countedTree);
            setLoadedKeys(newLoadedKeys);
            resolve(treeLeafs);
          })
          .catch((err) => {
            setTree(
              setChildrenToLazyTree(treeDataRef.current, nodeKey, []),
            );
            setExpandedKeys?.(loadedKeys);
            if (err.message !== 'canceled') {
              appErrorNotification(
                `Произошла ошибка при загрузке листьев дерева по ключу ${itemId}. Хук useLazyTreeDataWithPagination`,
                err,
              );
            }
            reject(err);
          });
      }),
    [
      loadedKeys,
      endpoint,
      cabinetId,
      isCabinet,
      setTree,
      updateChildren,
      setExpandedKeys,
      loadNodeChildren,
      resetPagination,
    ],
  );

  const onLoadData = useCallback(
    async (node: EventDataNode<TreeElement>) => {
      if (node.children && node.children.length > 0) {
        return Promise.resolve([]);
      }
      return refetchNode(node, false);
    },
    [refetchNode],
  );

  return {
    loadedKeys,
    loadedKeysSet,
    setLoadedKeys,
    onLoadData: useLoadQueue(onLoadData),
    refetchNode,
    // Новые методы для пагинации
    loadMoreItems,
    paginationState,
    resetPagination,
  };
};
