import { DatePicker, Input, Radio, Select } from 'antd';
import moment from 'moment';
import type { FC } from 'react';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import { hooks, selectors, enums, reducers, actions } from '../../store';
import styles from './styles.module.scss';

type FormModalProps = {
  onDBResponse: Callback;
  onSearch: Callback;
};

const Form: FC<FormModalProps> = ({ onDBResponse, onSearch }) => {
  const {
    setInputValues,
    setRepositories,
    setDetalization,
    setImportStatus,
    handleFormDataClear,
  } = useCreateSliceActions(reducers.slice.actions);

  const formData = useAppSelector(selectors.uploadLogFormDataSelector);
  const repositories = useAppSelector(selectors.repositoriesSelector);
  const isPending = useAppSelector(selectors.uploadLogApiPendingSelector);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.containerContent}>
          <p className={styles.containerContentText}>Найти:</p>
          <Input
            maxLength={4000}
            className={styles.containerContentAction}
            placeholder="Введите ИД пакета: например: bb71d9687ba81c5c394d48b3a1cddabe4b2f1bf1"
            value={formData.title}
            name="title"
            disabled={isPending}
            onChange={(event) =>
              setInputValues({ input: 'title', value: event.target.value })
            }
          />
        </div>

        <div className={styles.containerContent}>
          <p className={styles.containerContentText}>За период:</p>
          <DatePicker.RangePicker
            className={styles.containerContentAction}
            allowClear={false}
            format={DATE_VARIANTS_LIST}
            disabled={isPending}
            value={[
              moment(formData.periodFrom, DEFAULT_DATE_VARIANT),
              moment(formData.periodTo, DEFAULT_DATE_VARIANT),
            ]}
            onChange={(dates, dateStrings) => {
              setInputValues({ input: 'periodFrom', value: dateStrings[0] });
              setInputValues({ input: 'periodTo', value: dateStrings[1] });
            }}
          />
        </div>

        <div className={styles.containerContent}>
          <p className={styles.containerContentText}>Репозиторий:</p>
          <Select
            className={styles.containerContentAction}
            loading={repositories.isPending}
            mode="multiple"
            disabled={isPending}
            value={formData.repositories}
            onChange={setRepositories}
            allowClear
          >
            {repositories.repos.map((repo) => (
              <Select.Option value={repo.value} key={repo.value + repo.label}>
                {repo.label}
              </Select.Option>
            ))}
          </Select>
        </div>

        <div className={styles.containerContent}>
          <p className={styles.containerContentText}>Детализация сообщений:</p>
          <Radio.Group
            disabled={isPending}
            value={formData.detalization}
            className={styles.containerContentAction}
            onChange={(event) => setDetalization(event.target.value)}
          >
            <Radio value={enums.Detalization.ALL}>Все сообщений</Radio>
            <Radio value={enums.Detalization.PACKAGE}>Уровень пакета</Radio>
            <Radio value={enums.Detalization.DOCUMENT}>Уровень файла</Radio>
          </Radio.Group>
        </div>

        <div className={styles.containerContent}>
          <p className={styles.containerContentText}>Статус:</p>
          <Radio.Group
            value={formData.importStatus}
            className={styles.containerContentAction}
            onChange={(event) => setImportStatus(event.target.value)}
            disabled={isPending}
          >
            <Radio value={enums.ImportStatus.ALL}>Все</Radio>
            <Radio value={enums.ImportStatus.SUCCESSFULLY}>
              Успешно загруженные
            </Radio>
            <Radio value={enums.ImportStatus.FAILURE}>
              Ошибочно загруженные
            </Radio>
            <Radio value={enums.ImportStatus.REQUESTED}>
              Запрашиваемые пакеты
            </Radio>
          </Radio.Group>
        </div>
      </div>

      <div className={styles.buttonsRow}>
        <ButtonsContainer
          buttons={[
            {
              title: 'Найти',
              key: 'search',
              onClick: onSearch,
              disabled: isPending,
            },
            {
              title: 'Очистить',
              danger: true,
              ghost: true,
              key: 'clear',
              onClick: handleFormDataClear,
              disabled: isPending,
            },
          ]}
        />

        <ButtonsContainer
          buttons={[
            {
              title: 'Выгрузка из БД',
              key: 'download-btn',
              onClick: onDBResponse,
              disabled: isPending,
              type: 'primary',
            },
          ]}
        />
      </div>
    </>
  );
};

export const FormModal: FC<{ getTableData: Callback }> = ({ getTableData }) => {
  const [isFormOpen, , closeForm] = hooks.useFormModal();

  const dispatch = useAppDispatch();

  return (
    <AppPopup
      isOpened={isFormOpen}
      onClose={closeForm}
      title="Настройка отчета синхронизации с ФХ"
      className={styles.popup}
    >
      <Form
        onSearch={getTableData}
        onDBResponse={() => dispatch(actions.downloadDBReportThunk())}
      />
    </AppPopup>
  );
};
