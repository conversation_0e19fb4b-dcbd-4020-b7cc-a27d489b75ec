import { FC, useCallback } from 'react';
import {
  FilesVisibility,
  EpcPermissions,
  UseTreeTypes,
  WorkGroupEPCStore,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import { RequestStatuses } from 'shared/model';
import { CopyModalWithTree } from '../CopyModalWithTree';
import { EditModal } from '../EditModal';
import { EPCDownloadModal } from '../EPCDownloadModal';
import { VisibilityFilesModal } from '../VisibilityFilesModal';
import { WatchModal } from '../WatchModal';

type ModalsProps = {
  cabinetId: string;
  epcPermissions: EpcPermissions;
  popup: Record<string, boolean>;
  saveStatus: FilesVisibility;
  statuses: RequestStatuses<unknown>;
  togglePopup: (
    name: keyof typeof WorkGroupEPCConfig.popupsInitial,
    status?: boolean | undefined,
  ) => void;
} & UseTreeTypes;

export const Modals: FC<ModalsProps> = (props) => {
  const {
    cabinetId,
    epcPermissions,
    popup,
    togglePopup,
    treeCheck,
    treeLoad,
    treeActions,
    treeDrag,
    treeExpand,
    treeLink,
    tree,
    statuses,
    saveStatus,
  } = props;
  const [settings, setSettings, resetSettings] =
    WorkGroupEPCStore.hooks.useAdditionalSettings();

  const closeEditModal = useCallback(() => {
    if (popup.watch && tree.linkedData.editLinked.length === 0) {
      togglePopup('watch');
    }
    togglePopup('edit');
  }, [popup.watch, togglePopup, tree.linkedData.editLinked.length]);

  return (
    <>
      {popup.watch && (
        <WatchModal
          isLinkedReqEnd={treeLink.isLinkedReqEnd}
          setIsLinkedReqEnd={treeLink.setIsLinkedReqEnd}
          epcPermissions={epcPermissions}
          handleLinkClick={(isDirectory, itemId) => {
            treeDrag.setIsDrag(false);
            treeLink.handleLinkClick(isDirectory, itemId);
          }}
          handleClose={() => togglePopup('watch')}
          togglePopup={togglePopup}
        />
      )}

      {popup.edit && (
        <EditModal
          handleClose={closeEditModal}
          refetch={treeActions.refetchTree}
          treeLoad={treeLoad}
          treeExpand={treeExpand}
        />
      )}
      <EPCDownloadModal
        cabinetId={cabinetId}
        selectedFiles={treeCheck.checked.selectedFiles}
        selectedDirs={treeCheck.checked.selectedDirs}
        loadedKeys={treeLoad.loadedKeys}
        isOpened={popup.downloadEPC}
        handleClose={() => {
          resetSettings();
          togglePopup('downloadEPC');
        }}
        resetCheckboxes={treeCheck.resetCheck}
        settings={settings}
        setSettings={setSettings}
      />

      <VisibilityFilesModal
        isVisibilityPending={statuses.isPending}
        handleSave={saveStatus}
        isOpened={popup.visibility}
        handleClose={() => togglePopup('visibility')}
        checkedNodes={treeCheck.checked.nodes}
        refetchTree={treeActions.refetchTree}
        refetchNode={treeActions.refetchNode}
      />

      <CopyModalWithTree
        epcPermissions={epcPermissions}
        treeLoad={treeLoad}
        treeExpand={treeExpand}
        selectedNodes={treeCheck.checked.nodes}
        selectedFiles={treeCheck.checked.selectedFiles}
        refetchNode={treeActions.refetchNode}
        handleClose={() => togglePopup('copy')}
        isOpened={popup.copy}
        cabinetId={cabinetId}
      />
    </>
  );
};
