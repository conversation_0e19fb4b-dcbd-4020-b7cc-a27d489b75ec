import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LoadingStatusState } from '../types';
import { getLoadingStatus } from './thunks';

const initialState: LoadingStatusState = {
  text: '',
  currentFinishDate: '',
  current: 0,
  total: 0,
  isLoadingDatabase: false,
  isPending: false,
  error: null,
};

export const slice = createSlice({
  name: 'loadingStatus',
  initialState,
  reducers: {
    setLoadingStatus: (
      state,
      { payload }: PayloadAction<Partial<LoadingStatusState>>,
    ) => ({ ...state, ...payload }),
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getLoadingStatus.pending, (state) => {
        state.isPending = true;
        state.error = null;
      })
      .addCase(getLoadingStatus.fulfilled, (state, action) => {
        state.isPending = false;
        state.currentFinishDate = action.payload.currentFinishDate;
        state.isLoadingDatabase = action.payload.isLoading;
        state.text = action.payload.text;
        state.current = action.payload.current;
        state.total = action.payload.total;
      })
      .addCase(getLoadingStatus.rejected, (state, action) => {
        state.isPending = false;
        state.error =
          action.error.message || 'Произошла ошибка при загрузке статуса';
      });
  },
});
