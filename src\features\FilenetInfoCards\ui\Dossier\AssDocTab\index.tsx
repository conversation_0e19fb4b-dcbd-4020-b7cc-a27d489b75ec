import { Table } from 'antd';
import { FC, useState, useCallback, useEffect, useMemo } from 'react';
import { useEffectOnce } from 'react-use';
// TODO: убрать после переноса датагрида в ui либу
// eslint-disable-next-line boundaries/element-types
import { TableColumnData, TableRowData } from 'features/DataGrid';
import { FilenetActions } from 'entities/FilenetActions';
import { FilenetResponseData } from 'entities/FilenetAttachmentsTree';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { errorMessages } from 'shared/config';
import { appErrorNotification } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';
import { ApiContainer } from 'shared/ui';

import styles from './styles.module.scss';

const tabsColumns: TableColumnData[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 0.6,
  },
  {
    title: 'Наименование файла',
    dataIndex: 'title',
    key: 'title',
    width: 2,
  },
  {
    title: 'Размер',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 1,
  },
  {
    title: 'Изменен',
    dataIndex: 'modifiedGibrDatetime',
    key: 'modifiedGibrDatetime',
    width: 1.8,
  },
  {
    title: 'Загружен в ИВС ГИБР',
    dataIndex: 'uploadGibrDatetime',
    key: 'uploadGibrDatetime',
    width: 1.8,
  },
  {
    title: 'Загружен в КЗ ИД',
    dataIndex: 'uploadDatetime',
    key: 'uploadDatetime',
    width: 1.8,
  },
  {
    title: 'Тип файла',
    dataIndex: 'documentTypeExternalId',
    key: 'documentTypeExternalId',
    width: 1,
    render: (text) => (
      <div className={styles.cell}>
        {text}
        {/* &nbsp; */}
        {/* TODO: понять что за кнопка */}
        {/* <Popover content={<div />} trigger="click" placement="bottom">*/}
        {/*  <Button type="primary" size="small">*/}
        {/*    ...*/}
        {/*  </Button>*/}
        {/* </Popover>*/}
      </div>
    ),
  },
  {
    title: 'Действия',
    dataIndex: 'actions',
    key: 'actions',
    width: 1.5,
    render: (_id, row) => (
      <FilenetActions
        permissions={row.viewDownloadPermissions ?? []}
        cardId={row.id}
        attachment={(() => {
          const paths = String(row.title).split('/');

          return {
            id: row.attachmentId,
            filename: String(paths.at(-1)),
          };
        })()}
        size="small"
      />
    ),
    hideSorter: true,
    hideColumnSearch: true,
  },
];

interface AssDocsResponse {
  isLastPage: boolean;
  items: FilenetResponseData[];
}

const PAGE_SIZE = 5;

export const AssDocTab: FC<{
  popupId: string | number;
  tabKey: string;
}> = ({ popupId, tabKey }) => {
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  const [trigger, { isPending, error, data }] =
    useAxiosRequest<AssDocsResponse>(filenetServiceInstance);

  const rows: TableRowData[] = useMemo(
    () =>
      data
        ? data?.items?.map((item) => ({
            ...item,
            key: item.id as number,
          }))
        : [],
    [data],
  );

  useEffect(() => {
    if (data) {
      const newTotal = data.isLastPage
        ? (page - 1) * PAGE_SIZE + (data?.items?.length ?? 0)
        : (page + 1) * PAGE_SIZE;
      setTotal(newTotal);
    }
  }, [data, page]);

  const fetchData = useCallback(
    (pageNumber: number = page) =>
      trigger(apiUrls.fileNet.assDocsFiles, {
        method: 'POST',
        data: {
          ids: [popupId],
          groupExternalId: tabKey,
          offset: (pageNumber - 1) * PAGE_SIZE,
          size: PAGE_SIZE,
        },
      }).catch((err) => {
        appErrorNotification(
          'Произошла ошибка при загрузке приклепленных файлов',
          err as AppError,
        );
        throw err;
      }),
    [page, trigger, popupId, tabKey],
  );

  const handlePageChange = async (newPage: number): Promise<void> => {
    await fetchData(newPage);
    setPage(newPage);
  };

  // Загрузка при монтировании
  useEffectOnce(() => {
    fetchData();
  });

  return (
    <ApiContainer
      error={error}
      isPending={false}
      errorTitle={errorMessages.pendingError}
    >
      <Table
        loading={isPending}
        pagination={{
          current: page,
          total,
          pageSize: PAGE_SIZE,
          onChange: handlePageChange,
        }}
        columns={tabsColumns}
        dataSource={rows}
      />
    </ApiContainer>
  );
};

export default AssDocTab;
