import { WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';

/**
 * Определяет, нужно ли использовать пагинацию для узла
 *
 * @param node - Узел дерева
 * @returns True, если нужна пагинация
 */
export const shouldUsePaginationForNode = (node: TreeElement): boolean => {
  // Пагинация только для директорий
  if (!node.isDirectory) {
    return false;
  }

  // Проверяем количество дочерних элементов
  const childrenCount = node.childrenCount || node.totalCountOfLeafs || 0;
  const shouldPaginate = childrenCount > WorkGroupEPCConfig.PAGINATION_THRESHOLD;

  // Отладочная информация
  console.log('shouldUsePaginationForNode:', {
    nodeKey: node.key,
    title: node.title,
    isDirectory: node.isDirectory,
    childrenCount,
    totalCountOfLeafs: node.totalCountOfLeafs,
    threshold: WorkGroupEPCConfig.PAGINATION_THRESHOLD,
    shouldPaginate,
  });

  return shouldPaginate;
};
