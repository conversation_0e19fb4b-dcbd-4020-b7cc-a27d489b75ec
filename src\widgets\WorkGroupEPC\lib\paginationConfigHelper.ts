import { PAGINATION_CONFIG, DEBUG_PAGINATION } from '../config/constants';

/**
 * Тип узла дерева для определения конфигурации пагинации
 */
export type NodeType = 'directory' | 'systemDirectory' | 'largeCatalog';

/**
 * Конфигурация пагинации для узла
 */
export interface NodePaginationConfig {
  /** Размер страницы по умолчанию */
  defaultPageSize: number;
  /** Пороговое значение для показа Load More */
  threshold: number;
  /** Максимальный размер страницы */
  maxPageSize: number;
}

/**
 * Определяет тип узла на основе его свойств
 * @param node - Узел дерева
 * @returns Тип узла для конфигурации пагинации
 */
export const determineNodeType = (node: TreeElement): NodeType => {
  // Системная директория (isFixed = 2)
  if (node.isFixed === 2) {
    return 'systemDirectory';
  }

  // Большой каталог (много файлов)
  if (node.totalCountOfLeafs && node.totalCountOfLeafs > 100) {
    return 'largeCatalog';
  }

  // Обычная директория
  return 'directory';
};

/**
 * Получает конфигурацию пагинации для узла
 * @param node - Узел дерева
 * @returns Конфигурация пагинации
 */
export const getNodePaginationConfig = (node: TreeElement): NodePaginationConfig => {
  const nodeType = determineNodeType(node);
  const config = PAGINATION_CONFIG[nodeType];

  if (DEBUG_PAGINATION) {
    console.log(`[Pagination Config] Node ${node.key} (${node.title}) - Type: ${nodeType}`, config);
  }

  return config;
};

/**
 * Определяет, нужно ли использовать пагинацию для узла
 * @param node - Узел дерева
 * @returns true, если нужна пагинация
 */
export const shouldUsePaginationForNode = (node: TreeElement): boolean => {
  // Пагинация только для директорий
  if (!node.isDirectory) {
    return false;
  }

  const config = getNodePaginationConfig(node);
  
  // Проверяем по количеству листьев
  if (typeof node.totalCountOfLeafs === 'number') {
    return node.totalCountOfLeafs > config.threshold;
  }

  // Проверяем по количеству детей первого уровня
  if (typeof node.childrenCount === 'number') {
    return node.childrenCount > config.threshold;
  }

  // По умолчанию не используем пагинацию
  return false;
};

/**
 * Получает размер страницы для узла
 * @param node - Узел дерева
 * @param customPageSize - Пользовательский размер страницы (опционально)
 * @returns Размер страницы
 */
export const getPageSizeForNode = (
  node: TreeElement, 
  customPageSize?: number
): number => {
  const config = getNodePaginationConfig(node);
  
  if (customPageSize) {
    // Ограничиваем пользовательский размер максимальным значением
    return Math.min(customPageSize, config.maxPageSize);
  }

  return config.defaultPageSize;
};

/**
 * Создает отладочную информацию о пагинации узла
 * @param node - Узел дерева
 * @returns Строка с отладочной информацией
 */
export const getNodePaginationDebugInfo = (node: TreeElement): string => {
  const nodeType = determineNodeType(node);
  const config = getNodePaginationConfig(node);
  const shouldPaginate = shouldUsePaginationForNode(node);

  return [
    `Node: ${node.key} (${node.title})`,
    `Type: ${nodeType}`,
    `Should paginate: ${shouldPaginate}`,
    `Config: ${JSON.stringify(config)}`,
    `Total leafs: ${node.totalCountOfLeafs || 'unknown'}`,
    `Children count: ${node.childrenCount || 'unknown'}`,
  ].join('\n');
};

/**
 * Логирует информацию о пагинации узла (только в режиме отладки)
 * @param node - Узел дерева
 * @param action - Действие (например, 'load', 'loadMore')
 */
export const logPaginationInfo = (node: TreeElement, action: string): void => {
  if (DEBUG_PAGINATION) {
    console.group(`[Pagination Debug] ${action.toUpperCase()}`);
    console.log(getNodePaginationDebugInfo(node));
    console.groupEnd();
  }
};

/**
 * Валидирует параметры пагинации
 * @param page - Номер страницы
 * @param pageSize - Размер страницы
 * @param node - Узел дерева (для получения максимального размера страницы)
 * @returns Объект с результатом валидации
 */
export const validatePaginationParams = (
  page: number,
  pageSize: number,
  node?: TreeElement
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Проверка номера страницы
  if (!Number.isInteger(page) || page < 1) {
    errors.push('Page number must be a positive integer');
  }

  // Проверка размера страницы
  if (!Number.isInteger(pageSize) || pageSize < 1) {
    errors.push('Page size must be a positive integer');
  }

  // Проверка максимального размера страницы
  if (node) {
    const config = getNodePaginationConfig(node);
    if (pageSize > config.maxPageSize) {
      errors.push(`Page size cannot exceed ${config.maxPageSize} for this node type`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
