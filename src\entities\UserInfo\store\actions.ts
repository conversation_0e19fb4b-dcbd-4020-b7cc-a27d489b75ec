import { createAsyncThunk } from '@reduxjs/toolkit';
import { apiUrls, emptyBaseUrlInstance } from 'shared/api';
import { ErrorWithoutShow } from 'shared/model';
import { AuthDto } from './types';

export const getAuthenticatedThunk = createAsyncThunk<AuthDto, void>(
  'userInfo/getSideBarButtonsThunk',
  async () => {
    const { data } = await emptyBaseUrlInstance.get<AuthDto>(apiUrls.auth);

    return data;
  },
  {
    serializeError() {
      return new ErrorWithoutShow();
    },
  },
);
