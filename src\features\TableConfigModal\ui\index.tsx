import { FC } from 'react';

import { TableConfigModalProps } from 'features/TableConfigModal';
import { renderPopup, RenderPopupProps } from 'shared/model';
import { ApiContainer, ButtonsContainer } from 'shared/ui';

import { text } from '../config';
import { parseRowsToColumns } from '../libs';
import { hooks } from '../store';

import { InputForm } from './InputForm';
import { SelectRow } from './SelectRow';

import styles from './styles.module.scss';
import { Table } from './Table';

const TableConfigModal: FC<TableConfigModalProps> = ({
  onProfileSet,
  tableSizes,
  columns,
  endpoint,
  onClose,
  currentSelectedProfile,
}) => {
  const {
    selectedProfile,
    statuses,
    getAll,
    presetCodeStatuses,
    onReset,
    onPositionChange,
    onCheck,
    onDownload,
    onSave,
    onDelete,
    get,
    rows,
    onSort,
    sortValue,
  } = hooks.useConfigActions(
    endpoint,
    columns,
    tableSizes,
    currentSelectedProfile,
  );

  return (
    <ApiContainer
      error={presetCodeStatuses.error}
      isPending={presetCodeStatuses.isPending && presetCodeStatuses.isRefetch}
      refresh={getAll}
    >
      <SelectRow
        selectedProfile={selectedProfile}
        isPending={presetCodeStatuses.isPending || statuses.isPending}
        selectValues={presetCodeStatuses.data || []}
        onDownload={onDownload}
        onDelete={onDelete}
        onSelectChange={async (selectValue) => {
          await get(selectValue);
        }}
        isError={Boolean(statuses.error)}
      />

      <InputForm
        isPending={false}
        onSave={onSave}
        isError={Boolean(statuses.error)}
      />

      <Table
        rows={rows}
        onCheck={onCheck}
        onPositionChange={onPositionChange}
        onSort={onSort}
      />

      <ButtonsContainer
        buttons={[
          {
            key: 'confirm',
            title: text.confirmTitle,
            type: 'primary',
            disabled: statuses.isPending,
            onClick: () => {
              if (columns?.length > 0) {
                onProfileSet({
                  asRows: rows,
                  asColumns: parseRowsToColumns(rows, columns),
                  sortValue,
                });
              }
              onClose();
            },
          },
          {
            key: 'reset',
            title: text.reset,
            type: 'primary',
            ghost: true,
            disabled: statuses.isPending,
            onClick: onReset,
          },
          {
            key: 'close',
            title: text.closeTitle,
            type: 'ghost',
            danger: true,
            onClick: () => {
              onClose();
            },
          },
        ]}
      />
    </ApiContainer>
  );
};

export const createTableConfigModal = (
  props: Omit<TableConfigModalProps, 'onClose'>,
): void => {
  const options: RenderPopupProps = {
    title: text.title,
    className: styles.container,
    popupUI: ({ onClose }) => <TableConfigModal {...props} onClose={onClose} />,
  };

  renderPopup(options);
};
