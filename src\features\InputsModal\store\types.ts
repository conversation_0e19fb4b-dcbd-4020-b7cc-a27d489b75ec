export interface InputsModalProps {
  /** Ендпоинт */
  endpoint: Endpoint;
  /** Массив инпутов, не может быть пустым */
  inputs: NonEmptyArray<PopupInput>;
  /** Колбэк на закрытие */
  onClose: () => void;
  /** Колбэк на сабмит */
  onSubmit: () => Promise<void>;
  /** Титул */
  title: Title;
  /** Опциональный ключи для добавления в пейлоад тела */
  additionalBodyKeys?: Record<string, unknown>;
  /** Опциональный колбэк боди парсер */
  bodyParser?: (
    inputValues: Record<string, unknown>,
  ) => Record<string, unknown>;
  onSuccessSubmit?: (body: Record<string, unknown>) => void;
  /** Опциональное сообщение об ошибкe */
  rejectMessage?: string;
  // eslint-disable-next-line
  responseBody?: (res: any) => void;
  /** Опциональное сообщение об успехе */
  successMessage?: string;
}

interface PopupInputText<Key> {
  defaultValue: string;
  key: Key;
  title: Title;
  type: 'text';
  disabled?: boolean;
  hideOnRequest?: boolean;
  placeholder?: string;
  required?: boolean;
}

interface PopupInputNumber<Key> {
  defaultValue: number;
  key: Key;
  title: Title;
  type: 'number';
  hideOnRequest?: boolean;
  required?: boolean;
}

interface PopupInputCheckbox<Key> {
  defaultChecked: boolean;
  key: Key;
  title: Title;
  type: 'checkbox';
  hideOnRequest?: boolean;
  required?: boolean;
}

interface PopupInputCheckbox<Key> {
  defaultChecked: boolean;
  key: Key;
  title: Title;
  type: 'checkbox';
  hideOnRequest?: boolean;
  required?: boolean;
}

export type PopupInput<Key = string> =
  | PopupInputCheckbox<Key>
  | PopupInputText<Key>
  | PopupInputNumber<Key>;
