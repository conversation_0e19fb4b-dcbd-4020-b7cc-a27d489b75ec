import { Divider } from 'antd';
import classNames from 'classnames';
import { FC } from 'react';

import {
  createBasicClosableNotice,
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';

import { constants } from '../../config';
import { createModalButtonsList } from '../../lib';
import { selectors, actions, reducers } from '../../store';

import styles from './styles.module.scss';

/** Подкомпонент модалього окна юзеров */
export const UserModal: FC = ({ children }) => {
  const isUsersModalOpen = useAppSelector(selectors.userModalSelector);
  const isPending = useAppSelector(selectors.userModalPendingSelector);
  const { closeUsersModal, clearUserModal } = useCreateSliceActions(
    reducers.slice.actions,
  );

  const dispatch = useAppDispatch();

  return (
    <AppPopup
      isOpened={isUsersModalOpen}
      onClose={closeUsersModal}
      className={classNames(styles.popup, styles.popupUsers)}
      title="Пользователи системы"
    >
      <Divider />
      {children}
      <Divider />
      <ButtonsContainer
        className={styles.buttons}
        buttons={createModalButtonsList(
          () =>
            dispatch(actions.postUserFormThunk())
              .unwrap()
              .then(() => {
                clearUserModal();
                createBasicClosableNotice({
                  description: constants.POPUP_DESCRIPTION,
                  message: constants.POPUP_MESSAGE,
                });
              }),
          clearUserModal,
          closeUsersModal,
          isPending,
        )}
      />
    </AppPopup>
  );
};
