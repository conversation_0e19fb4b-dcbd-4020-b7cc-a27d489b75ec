import { useCallback } from 'react';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { reducers, selectors } from '.';

type UseModalReturn = [boolean, Callback, Callback];

export const useFSyncDiskModal = (): UseModalReturn => {
  const isDiskOpen = useAppSelector(selectors.isDiskOpenSelector);
  const { handleModalState } = useCreateSliceActions(reducers.slice.actions);

  const openDiskModal = useCallback(
    () => handleModalState({ status: true, popup: 'isDiskOpen' }),
    [handleModalState],
  );
  const closeDiskModal = useCallback(
    () => handleModalState({ status: false, popup: 'isDiskOpen' }),
    [handleModalState],
  );

  return [isDiskOpen, openDiskModal, closeDiskModal];
};

export const useFormModal = (): UseModalReturn => {
  const isFormOpen = useAppSelector(selectors.isFormOpenSelector);
  const { handleModalState } = useCreateSliceActions(reducers.slice.actions);

  const openFormModal = useCallback(
    () => handleModalState({ status: true, popup: 'isFormOpen' }),
    [handleModalState],
  );
  const closeFormModal = useCallback(
    () => handleModalState({ status: false, popup: 'isFormOpen' }),
    [handleModalState],
  );

  return [isFormOpen, openFormModal, closeFormModal];
};
