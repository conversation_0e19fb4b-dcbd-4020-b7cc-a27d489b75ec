import { useCallback, useState } from 'react';
import { FiltersCallback, FiltersInitial, FetchFilteredData } from '../types';

const filtersInitial: FiltersInitial = [];
export const useColumnFilters = ({
  fetchFilteredData,
}: {
  fetchFilteredData?: FetchFilteredData;
}): [
  FiltersCallback,
  FiltersInitial,
  Callback,
  FiltersCallback,
  React.Dispatch<React.SetStateAction<FiltersInitial>>,
] => {
  const [filters, setFilters] = useState<FiltersInitial>([]);

  const handleFilters = useCallback<FiltersCallback>(
    (values) => {
      const filtered = filters.filter((item) => item.column !== values.column);

      setFilters((prevState) => {
        const newFilters = prevState.some(
          (item) => item.column === values.column,
        )
          ? [...filtered, values]
          : [...prevState, values];

        fetchFilteredData?.(newFilters);
        return newFilters;
      });
    },
    [filters, fetchFilteredData],
  );

  const handleResetColumn = useCallback<FiltersCallback>(
    (values) => {
      setFilters((prevState) => {
        const newFilters = [
          ...prevState.filter((item) => item.column !== values.column),
        ];
        fetchFilteredData?.(newFilters);
        return newFilters;
      });
    },
    [fetchFilteredData],
  );

  const resetFilters = useCallback(() => {
    setFilters(filtersInitial);
    fetchFilteredData?.(filtersInitial);
  }, [fetchFilteredData]);

  return [handleFilters, filters, resetFilters, handleResetColumn, setFilters];
};
