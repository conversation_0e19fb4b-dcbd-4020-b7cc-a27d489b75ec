import { renderHook, cleanup, act } from '@testing-library/react-hooks/pure';
import { wrapper } from 'shared/lib/testingUtils';
import { useTableSearch } from '../store/hooks';
import type { TableColumnData, TableRowData } from '..';

describe('useTableSearch', () => {
  afterEach(cleanup);

  it('Должен вернуть переданный массив, если filterQuery пустой', () => {
    const rows: TableRowData[] = [
      { tabTitle: 'test', key: '1' },
      { tabTitle: 'test2', key: '2' },
    ];
    const columns: TableColumnData[] = [
      {
        dataIndex: 'tabTitle',
      },
    ];

    const {
      result: { current },
    } = renderHook(() => useTableSearch(rows, columns), { wrapper });

    expect(current[0]).toEqual(rows);
  });

  it('Должен вернуть массив с фильтрованными значениями', async () => {
    const rows: TableRowData[] = [
      { tabTitle: 'test', key: '1' },
      { tabTitle: 'not filtered', key: '2' },
    ];
    const columns: TableColumnData[] = [
      {
        dataIndex: 'tabTitle',
      },
    ];

    const { result, waitForNextUpdate } = renderHook(
      () => useTableSearch(rows, columns),
      { wrapper },
    );

    expect(result.current[0]).toHaveLength(2);

    act(() => {
      result.current[2]('test');
    });

    await waitForNextUpdate();

    expect(result.current[0]).toHaveLength(1);
    expect(result.current[0][0]).toEqual(rows[0]);
  });
});
