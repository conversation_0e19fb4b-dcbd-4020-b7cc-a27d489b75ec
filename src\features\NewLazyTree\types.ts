import { TreeProps } from 'antd';

/** Идентификатор корневого узла дерева */
type RootId = string;

/** Идентификатор дочернего узла дерева */
type ChildrenId = string;

/** Состояние дерева: структура и статус загрузки */
export type TreeState = {
  /** Отмеченные узлы */
  checkedKeys: Record<string, boolean>;
  /** Данные дерева: ID узла -> TreeElement */
  entities: Record<ChildrenId, TreeElement | undefined>;
  /** Развернутые директории */
  expandedKeys: Record<string, boolean>;
  /** Загруженные директории */
  loadedKeys: Record<string, boolean>;
  /** Флаг загрузки корня дерева */
  loading: boolean;
  /** Загружaемые директории */
  loadingKeys: Record<string, boolean>;
  /** Идентификаторы корневых узлов */
  rootId: RootId[];
  selectedNodeId: string;
  /** Ошибка при загрузке */
  error?: string;
};

/** Состояние нескольких деревьев, каждая с уникальным ключом */
export type TreesState = {
  /** Данные по каждому дереву, ключ - RootId */
  trees: Record<RootId, TreeState | undefined>;
};

export type Endpoints = {
  delete: (id: string) => string;
  fetch: string;
  move: string;
};

export type TreeActions = {
  actionOnCheckedNodes: ({
    actionCallback,
    filterCallback,
    successMessage,
    updateCallback,
  }: {
    actionCallback: (nodes: TreeElement[]) => Promise<void>;
    filterCallback: (nodes: TreeElement) => boolean;
    successMessage: string;
    updateCallback?: (nodes: TreeElement) => void;
  }) => Promise<string>;
  deleteNode: ({
    itemId,
    parentId,
    fileNetId,
  }: {
    fileNetId: string;
    itemId: string;
    parentId: string;
  }) => Promise<string>;
  moveNode: (body: MoveDNDBody, node: TreeElement) => Promise<string>;
  refetchNode: (itemId: string) => Promise<void>;
  resetTree: () => Promise<void>;
  updateNodes: (payload: {
    treeKey: string;
    updatedList: {
      itemId: string;
      updatedParams: Partial<TreeElement>;
    }[];
  }) => void;
};

export type LoadData = NonNullable<TreeProps<TreeElement>['loadData']>;

export type TreeStatuses = {
  error: string | undefined;
  isLoading: boolean;
};
