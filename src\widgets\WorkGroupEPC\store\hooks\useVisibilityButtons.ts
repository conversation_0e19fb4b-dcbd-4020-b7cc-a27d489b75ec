import { notification } from 'antd';
import { useMemo } from 'react';
import {
  WorkGroupEPCConfig,
  WorkGroupEPCLib,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { FilesVisibility } from 'widgets/WorkGroupEPC/types';
import { appErrorNotification } from 'shared/lib';
import { getParentsNodes } from 'shared/lib/getParentNodes';
import { useAppSelector } from 'shared/model';

export const useVisibilityButtons = (
  checkedNodes: TreeElement[],
  isVisible: boolean,
  handleClose: Callback,
  handleSave: FilesVisibility,
  isPending: boolean,
  refetchTree: Callback,
  refetchNode: ({ itemId, isDirectory, key }: TreeElement) => Promise<void>,
): AdditionalButton[] => {
  const [fileIds, directoryIds, filteredNodes] = useMemo(
    () =>
      checkedNodes.reduce(
        ([files, dirs, nodes], node: TreeElement) => {
          if (
            node.cabinetOnly !== isVisible &&
            node?.key !== WorkGroupEPCConfig.ROOT_KEY
          ) {
            nodes.push(node);
            if (node.isDirectory) {
              dirs.push(node.itemId!);
            } else {
              files.push(node.itemId!);
            }
          }
          return [files, dirs, nodes];
        },
        [[], [], []] as [string[], string[], TreeElement[]],
      ),
    [checkedNodes, isVisible],
  );

  const epcData = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  const handleChangeVisibility = async (): Promise<void> => {
    await handleSave(fileIds, directoryIds, isVisible);
  };

  const refetchParentNodes = async (): Promise<void> => {
    const parents = getParentsNodes(
      epcData.treeData.tree,
      filteredNodes.map((node) => node.itemId as Key),
    );

    // Рефетчим только родителей верхнего уровня,
    const { filterTopLevelParents } = WorkGroupEPCLib;
    const topLevelParents = filterTopLevelParents(parents);
    topLevelParents.forEach((node: TreeElement) => {
      refetchNode(node);
    });
  };

  return [
    {
      title: 'Сохранить',
      key: 'save',
      loading: isPending,
      type: 'primary',
      onClick: async () => {
        try {
          await handleChangeVisibility();
          notification.success({
            message: isVisible
              ? 'Элементы доступны в режиме "только для КРГ"'
              : 'Элементы доступны для Контроля выполнения',
          });
          handleClose();
          setTimeout(() => {
            refetchParentNodes();
          }, WorkGroupEPCConfig.DELAY_FOR_REFETCH);
        } catch (err) {
          appErrorNotification(
            'Произошла ошибка изменения видимости',
            err as AppError,
          );
        }
      },
    },
    {
      title: 'Отмена',
      danger: true,
      loading: isPending,
      ghost: true,
      key: 'cancel',
      onClick: handleClose,
    },
  ];
};
