// import { http, HttpResponse } from 'msw';
// import { devServerUrl, FILENET_SERVICE } from 'shared/api';
// import { delay } from 'shared/lib/delay';
// import { fileCard, filesList, fileTree } from './mockData';

// const fileNethandlers = [
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/get/11743`,
//     async () => {
//       await delay();
//       return HttpResponse.json(fileCard, { status: 200 });
//     },
//   ),
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/v2/get/as/tree`,
//     async () => {
//       await delay(3000);
//       return HttpResponse.json(fileTree, { status: 200 });
//     },
//   ),
//   http.post(
//     `${devServerUrl}${FILENET_SERVICE}/main/find/documents`,
//     async () => {
//       await delay();
//       return HttpResponse.json(filesList, { status: 200 });
//     },
//   ),
// ];

export const handlers = [
  /* ...fileNethandlers */
];
