import React, { useState } from 'react';
import { Button, Card, Typography, Space, Divider, Alert } from 'antd';
import { LoadMoreButton } from 'shared/ui';
import { 
  getNodePaginationConfig, 
  shouldUsePaginationForNode, 
  getPageSizeForNode,
  getNodePaginationDebugInfo,
  logPaginationInfo,
  validatePaginationParams,
} from 'widgets/WorkGroupEPC';

const { Title, Text, Paragraph } = Typography;

/**
 * Тестовый компонент для проверки функциональности пагинации
 * Используется для отладки и демонстрации возможностей
 */
export const PaginationTestComponent: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [loadedCount, setLoadedCount] = useState(10);
  const [totalCount] = useState(150);

  // Тестовые узлы дерева
  const testNodes: TreeElement[] = [
    {
      key: 'test-1',
      title: 'Обычная директория',
      isDirectory: true,
      itemId: 'dir-1',
      totalCountOfLeafs: 25,
      childrenCount: 12,
    } as TreeElement,
    {
      key: 'test-2', 
      title: 'Системная директория',
      isDirectory: true,
      itemId: 'sys-dir-1',
      isFixed: 2,
      totalCountOfLeafs: 45,
      childrenCount: 20,
    } as TreeElement,
    {
      key: 'test-3',
      title: 'Большой каталог',
      isDirectory: true,
      itemId: 'large-dir-1',
      totalCountOfLeafs: 250,
      childrenCount: 80,
    } as TreeElement,
    {
      key: 'test-4',
      title: 'Файл (не должен использовать пагинацию)',
      isDirectory: false,
      itemId: 'file-1',
    } as TreeElement,
  ];

  const handleLoadMore = () => {
    setLoading(true);
    
    // Имитация загрузки
    setTimeout(() => {
      setLoadedCount(prev => Math.min(prev + 10, totalCount));
      setLoading(false);
    }, 1000);
  };

  const handleTestNode = (node: TreeElement) => {
    logPaginationInfo(node, 'test');
    console.log('=== Pagination Test Results ===');
    console.log(getNodePaginationDebugInfo(node));
  };

  const testValidation = () => {
    const testCases = [
      { page: 1, pageSize: 10, node: testNodes[0] },
      { page: 0, pageSize: 10, node: testNodes[0] }, // Невалидная страница
      { page: 1, pageSize: 100, node: testNodes[0] }, // Превышение максимального размера
      { page: 1, pageSize: -5, node: testNodes[0] }, // Отрицательный размер
    ];

    console.log('=== Validation Test Results ===');
    testCases.forEach((testCase, index) => {
      const result = validatePaginationParams(testCase.page, testCase.pageSize, testCase.node);
      console.log(`Test case ${index + 1}:`, testCase, 'Result:', result);
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <Title level={2}>Тестирование пагинации EPC дерева</Title>
      
      <Alert
        message="Тестовый компонент"
        description="Этот компонент предназначен для тестирования и отладки функциональности пагинации. В продакшене он должен быть удален."
        type="warning"
        showIcon
        style={{ marginBottom: '20px' }}
      />

      {/* Тест LoadMoreButton */}
      <Card title="Тест LoadMoreButton" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>Вариант по умолчанию:</Text>
            <div style={{ marginTop: '8px' }}>
              <LoadMoreButton
                onLoadMore={handleLoadMore}
                loading={loading}
                loadedCount={loadedCount}
                totalCount={totalCount}
                showCounter={true}
              />
            </div>
          </div>

          <div>
            <Text strong>Компактный вариант:</Text>
            <div style={{ marginTop: '8px' }}>
              <LoadMoreButton
                onLoadMore={handleLoadMore}
                loading={loading}
                loadedCount={loadedCount}
                totalCount={totalCount}
                variant="compact"
                showCounter={true}
              />
            </div>
          </div>

          <div>
            <Text strong>Минимальный вариант:</Text>
            <div style={{ marginTop: '8px' }}>
              <LoadMoreButton
                onLoadMore={handleLoadMore}
                loading={loading}
                variant="minimal"
              />
            </div>
          </div>
        </Space>
      </Card>

      {/* Тест конфигурации узлов */}
      <Card title="Тест конфигурации узлов" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {testNodes.map((node, index) => {
            const config = getNodePaginationConfig(node);
            const shouldPaginate = shouldUsePaginationForNode(node);
            const pageSize = getPageSizeForNode(node);

            return (
              <div key={node.key} style={{ padding: '12px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                <Text strong>{node.title}</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>Нужна пагинация: <Text code>{shouldPaginate ? 'Да' : 'Нет'}</Text></Text><br />
                  <Text>Размер страницы: <Text code>{pageSize}</Text></Text><br />
                  <Text>Порог: <Text code>{config.threshold}</Text></Text><br />
                  <Text>Макс. размер: <Text code>{config.maxPageSize}</Text></Text>
                </div>
                <div style={{ marginTop: '8px' }}>
                  <Button size="small" onClick={() => handleTestNode(node)}>
                    Тест в консоли
                  </Button>
                </div>
              </div>
            );
          })}
        </Space>
      </Card>

      {/* Тест валидации */}
      <Card title="Тест валидации параметров">
        <Paragraph>
          Нажмите кнопку ниже, чтобы запустить тесты валидации параметров пагинации.
          Результаты будут выведены в консоль браузера.
        </Paragraph>
        <Button onClick={testValidation}>
          Запустить тесты валидации
        </Button>
      </Card>
    </div>
  );
};

export default PaginationTestComponent;
