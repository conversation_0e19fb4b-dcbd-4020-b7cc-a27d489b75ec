import { notification } from 'antd';
import { Dispatch, SetStateAction, useCallback, useState } from 'react';
import { TogglePopup, WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { apiUrls } from 'shared/api';
import {
  collectNodesWithChildren,
  generateUrlWithQueryParams,
} from 'shared/lib';
import {
  TreeSelect,
  useAppDispatch,
  useCreateSliceActions,
} from 'shared/model';

type UseTreeSearch = [
  boolean,
  Dispatch<SetStateAction<boolean>>,
  (body: object) => Promise<void>,
];

export const useTreeSearch = (
  cabinetId: string,
  togglePopup: TogglePopup,
  treeValues: TreeSelect,
  loadedKeys: Key[],
  expandedKeys: Key[],
  setLoadedKeys: React.Dispatch<React.SetStateAction<React.Key[]>>,
  handleExpand: (keys: Key[]) => void,
): UseTreeSearch => {
  const dispatch = useAppDispatch();

  const { setIsPendingTree, handleResetSearchTags } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );
  const [isReqEnd, setIsReqEnd] = useState<boolean>(false);

  const handleSearch = useCallback(
    async (body: object): Promise<void> => {
      if (Object.keys(body).length === 0) {
        return;
      }

      const prevLoadedKeys = [...loadedKeys];
      const prevExpandedKeys = [...expandedKeys];

      setIsPendingTree(true);
      setIsReqEnd(false);

      const payload = await dispatch(
        WorkGroupEPCStore.actions.postSearchThunk({
          url: generateUrlWithQueryParams(
            apiUrls.workGroup.EPC.searchLazyTree,
            {
              cabinetId,
              isCabinet: true,
              itemId:
                treeValues.selectedKeys.length > 0
                  ? (treeValues.selectedNode as TreeElement).itemId
                  : undefined,
            },
          ),
          body,
        }),
      ).unwrap();

      const newLoadedKeys = collectNodesWithChildren(payload.treeData).map(
        (node) => node.key,
      );

      if (
        payload.treeData.length === 1 &&
        payload.treeData[0].children?.length === 0
      ) {
        notification.warning({ message: 'Поиск не дал результатов' });
        handleResetSearchTags();
        setLoadedKeys(prevLoadedKeys);
        handleExpand(prevExpandedKeys);
      } else {
        setLoadedKeys(newLoadedKeys);
        handleExpand(newLoadedKeys);
        setIsReqEnd(true);
      }

      setIsPendingTree(false);
      togglePopup('search');
    },
    [
      cabinetId,
      dispatch,
      expandedKeys,
      handleExpand,
      handleResetSearchTags,
      loadedKeys,
      setIsPendingTree,
      setLoadedKeys,
      togglePopup,
      treeValues.selectedKeys.length,
      treeValues.selectedNode,
    ],
  );

  return [isReqEnd, setIsReqEnd, handleSearch];
};
