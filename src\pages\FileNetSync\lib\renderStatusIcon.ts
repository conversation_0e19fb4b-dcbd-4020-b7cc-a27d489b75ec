import {
  AlertTwoTone,
  CheckCircleTwoTone,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { createElement } from 'react';
import { isDoneStatus, isErrorStatus } from 'shared/lib/fileNetStatusCheck';

export const renderStatusIcon = (status: string): JSX.Element => {
  if (isErrorStatus(status)) {
    return createElement(AlertTwoTone, { twoToneColor: '#ff7d7d' });
  }
  if (isDoneStatus(status)) {
    return createElement(CheckCircleTwoTone, { twoToneColor: '#52c41a' });
  }

  return createElement(ClockCircleOutlined);
};
