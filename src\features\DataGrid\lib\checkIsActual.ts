import moment from 'moment';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';

/**
 * Функция для проверки на актуальность
 *
 * @param {string} text - Строка содержащая дату в формате -{дата})
 * @returns {boolean} Возвращает булево с актуальностью
 */
export const checkIsActual = (text: string): boolean => {
  const regExp = /-\d{2}.\d{2}.\d{4}\)/;

  try {
    const dateArray = text.match(regExp);
    const dateAsMoment = moment(dateArray?.[0], DEFAULT_DATE_VARIANT);

    /* Приводит к читаемой дате для new Date и сравнивает по милисекундам */
    return dateAsMoment.isValid() && moment().isBefore(dateAsMoment);
  } catch {
    return false;
  }
};
