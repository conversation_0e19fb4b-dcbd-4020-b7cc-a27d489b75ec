import { useCallback } from 'react';
import { useSearchParamsState } from 'shared/model';

const TABS_IN_PERMISSION = 4;

export const useTabsActions = (): typeof useTabsActionsReturn => {
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */

  const [currentTab, setCurrentTab] = useSearchParamsState(
    'tab',
    '1',
    new RegExp(`^[1-${TABS_IN_PERMISSION}]$`),
  );

  /* ----------------------------------------------------
   *                      Колбэки
   ---------------------------------------------------- */

  const onTabChange = useCallback(
    (tabKey: string): void => {
      setCurrentTab(tabKey);
    },
    [setCurrentTab],
  );

  const useTabsActionsReturn = {
    currentTab,
    onTabChange,
  } as const;

  return useTabsActionsReturn;
};
