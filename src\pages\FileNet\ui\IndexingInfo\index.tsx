import { Tag } from 'antd';
import type { FC } from 'react';
import { getPluralEnding } from 'shared/lib/getPluralEnding';
import { hooks } from '../../store';

import styles from './styles.module.scss';

export const IndexingInfo: FC = () => {
  const [fileNetIndexingInfo] = hooks.useGetIndexingInfo();

  if (fileNetIndexingInfo.isPending) {
    return null;
  }

  const {
    lastImportingDate,
    enableContentIndexing,
    documentStatistic: { notContentIndexedCount, notIndexedCount },
  } = fileNetIndexingInfo.data!;

  return (
    <div className={styles.indexingInfo}>
      <Tag>
        {fileNetIndexingInfo.error
          ? 'Произошла ошибка при запросе информации о синхронизации!'
          : `Последняя синхронизация ${lastImportingDate}. В очереди на индексацию ${notIndexedCount} документ${getPluralEnding(
              notIndexedCount,
            )}, ${notContentIndexedCount} файл${getPluralEnding(
              notContentIndexedCount,
            )}${enableContentIndexing ? '.' : ' (отключена).'}`}
      </Tag>
    </div>
  );
};
