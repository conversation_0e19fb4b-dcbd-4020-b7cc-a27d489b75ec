import compose from 'compose-function';

import { withAntConfigProvider } from './withAntConfigProvider';
import { withAxiosInterceptors } from './withAxiosInterceptors';
import { withErrorBoundary } from './withErrorBoundary';
import { withReduxStore } from './withReduxStore';
import { withRouter } from './withRouter';
import { withWindowId } from './withWindowId';

export const withProviders = compose(
  withAntConfigProvider,
  withReduxStore,
  withErrorBoundary,
  withWindowId,
  withAxiosInterceptors,
  withRouter,
);
