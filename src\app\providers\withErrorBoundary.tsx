import type { FC, ComponentType } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import ErrorPage from 'pages/ErrorPage';

const ErrorFallback: ComponentType<FallbackProps> = ({ error }) => (
  <div role="alert">
    <ErrorPage error={error} />
  </div>
);

const myErrorHandler = (
  error: Error,
  info: { componentStack: string },
): void => {
  // eslint-disable-next-line no-console
  console.error({ error, info });
};

export const withErrorBoundary = (component: () => FC) => () =>
  (
    <ErrorBoundary FallbackComponent={ErrorFallback} onError={myErrorHandler}>
      {component()}
    </ErrorBoundary>
  );
