import { notification } from 'antd';
import classNames from 'classnames';
import { FC, useCallback, useLayoutEffect, useMemo } from 'react';
import { useToggle } from 'react-use';
import {
  WorkGroupCompositionStore,
  WorkGroupCompositionProps,
} from 'widgets/WorkGroupComposition';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ApiContainer } from 'shared/ui/ApiContainer';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { CreateUserTable } from './CreateUserTable';
import styles from './styles.module.scss';
import { UpdateUserSelect } from './UpdateUserSelect';

export const WorkGroupComposition: FC<WorkGroupCompositionProps> = ({
  title,
  onClose,
  endpoint,
  cabinetId,
  refetch,
  rowId,
  type,
  showOnlyFounded = true,
}) => {
  const dispatch = useAppDispatch();

  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const [isFullSize, toggleFullSize] = useToggle(false);

  /* ----------------------------------------------------
   *                      Экшены
   ---------------------------------------------------- */
  const { reset } = useCreateSliceActions(
    WorkGroupCompositionStore.reducers.slice.actions,
  );

  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */
  const composition = useAppSelector(
    WorkGroupCompositionStore.selectors.compositionSelector,
  );

  /* ----------------------------------------------------
   *                      Колбеки
   ---------------------------------------------------- */
  const getUserTable = useCallback(() => {
    dispatch(
      WorkGroupCompositionStore.actions.getDataThunk(
        generateUrlWithQueryParams(endpoint, { cabinetId }),
      ),
    );
  }, [endpoint, cabinetId]); // eslint-disable-line

  const getNestedUserTable = useCallback(
    async (userId) => {
      await dispatch(
        WorkGroupCompositionStore.actions.getNestedDataThunk({
          // krg4_create_user_children
          endpoint: `${endpoint}_children`,
          userId,
          cabinetId,
        }),
      ).unwrap();
    },
    [dispatch, endpoint, cabinetId],
  );

  const getSelect = useCallback(() => {
    dispatch(
      WorkGroupCompositionStore.actions.getPermissionsSelectThunk(
        generateUrlWithQueryParams(
          apiUrls.workGroupControl.composition.getSelect,
          {
            cabinetId,
            groupMemberId: rowId?.groupMemberId,
            memberIdFromPuir: rowId?.memberIdFromPuir,
          },
        ),
      ),
    );
  }, [cabinetId, rowId]); // eslint-disable-line

  /* ----------------------------------------------------
   *                    Мемоизация
   ---------------------------------------------------- */
  const buttons = useMemo<AdditionalButton[]>(
    () => [
      {
        title: 'Сохранить',
        key: 'save',
        disabled:
          composition.creation.isPending ||
          (type === 'create' &&
            composition.creation.table.rows.every((i) => !i.checked)),
        type: 'primary',
        onClick: async () => {
          const selectedUsers = composition.creation.table.rows.filter(
            (i) => i.checked,
          );

          const isSomeEmptyPermissions = selectedUsers.some(
            (item) => !item.permissions,
          );

          if (isSomeEmptyPermissions) {
            notification.warning({
              message:
                'Для выбранных пользователей необходимо указать дополнительные полномочия. Выберите полномочия из списка или отмените выбор пользователя.',
            });
            return;
          }

          const creationBody = {
            cabinetId,
            user: 'admin',
            selectedUsers: selectedUsers?.map((item) => ({
              permissions: item.permissions,
              memberId: item.rowId?.memberId,
            })),
          };

          const updateBody = {
            cabinetId,
            groupMemberId: rowId?.groupMemberId || '',
            additionalPermission: composition.update.select.selectedValue,
            memberIdFromPuir: rowId?.memberIdFromPuir || '',
          };

          await dispatch(
            WorkGroupCompositionStore.actions.postDataThunk({
              url: endpoint,
              body: { create: creationBody, update: updateBody }[type],
            }),
          )
            .unwrap()
            .then((res) => {
              if (res) {
                notification.success({
                  message: {
                    create: 'Пользователь успешно включен в КРГ',
                    update: 'Роль пользователя успешно изменена',
                  }[type],
                });

                refetch();
                onClose();
              } else {
                notification.warn({
                  message: {
                    create: 'Ошибка включения пользователя в КРГ',
                    update: 'Ошибка изменения роли пользователя',
                  }[type],
                });
              }
            });
        },
      },
      {
        title: 'Отмена',
        danger: true,
        disabled: composition.creation.isPending,
        key: 'cancel',
        ghost: true,
        onClick: () => {
          onClose();
        },
      },
    ],
    [composition, cabinetId, rowId], // eslint-disable-line
  );

  /* ----------------------------------------------------
   *                      Сайды
   ---------------------------------------------------- */
  useLayoutEffect(() => {
    getSelect();

    if (type === 'create') {
      getUserTable();
    }
    return () => reset();
  }, []); // eslint-disable-line

  return (
    <AppPopup
      isOpened
      onClose={onClose}
      title={title}
      additionalFullSizeHandler={(sizeStatus) => {
        if (type === 'create') {
          toggleFullSize(sizeStatus);
        }
      }}
      fullSizeClassName={type === 'create' ? styles.popupFullSize : undefined}
      className={classNames(
        styles.popup,
        type === 'update' && styles.popupSmall,
      )}
    >
      <div className={styles.popupContainer}>
        {
          {
            create: (
              <ApiContainer
                isPending={composition.creation.isPending}
                error={composition.creation.error}
                errorTitle="Произошла ошибка запроса пользователей"
              >
                <CreateUserTable
                  getNestedUserTable={getNestedUserTable}
                  isFullSize={isFullSize}
                  showOnlyFounded={showOnlyFounded}
                />
              </ApiContainer>
            ),
            update: <UpdateUserSelect />,
          }[type]
        }
      </div>

      <ButtonsContainer buttons={buttons} />
    </AppPopup>
  );
};
