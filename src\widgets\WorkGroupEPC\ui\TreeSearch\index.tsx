import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { FC } from 'react';
import { TogglePopup } from 'widgets/WorkGroupEPC';
import styles from './styles.module.scss';

interface TreeSearchProps {
  togglePopup: TogglePopup;
  treeSearchConfig: {
    arrowDown: Callback;
    arrowUp: Callback;
    currentIndex: number;
    tagged: TreeElement[];
  };
}

export const TreeSearch: FC<TreeSearchProps> = ({
  togglePopup,
  treeSearchConfig,
}) => (
  <div className={styles.search}>
    <Button onClick={() => togglePopup('search')}>Поиск</Button>
    {treeSearchConfig.tagged.length !== 0 && (
      <div className={styles.searchSwitch}>
        <Typography.Text>{`${treeSearchConfig.currentIndex}/${treeSearchConfig.tagged.length}`}</Typography.Text>
        <Button
          size="small"
          onClick={treeSearchConfig.arrowDown}
          icon={<ArrowDownOutlined />}
        />
        <Button
          size="small"
          onClick={treeSearchConfig.arrowUp}
          icon={<ArrowUpOutlined />}
        />
      </div>
    )}
  </div>
);
