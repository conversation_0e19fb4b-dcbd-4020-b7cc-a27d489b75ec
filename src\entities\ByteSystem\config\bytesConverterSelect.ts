import { BytesSystemConfig } from '..';

export const bytesConverterSelect: SelectData[] = [
  {
    title: BytesSystemConfig.formats.MB,
    key: '1',
    value: '1',
    label: BytesSystemConfig.bytesPow.MB,
  },
  {
    title: BytesSystemConfig.formats.GB,
    key: '2',
    value: '2',
    label: BytesSystemConfig.bytesPow.GB,
  },
  {
    title: BytesSystemConfig.formats.TB,
    key: '3',
    value: '3',
    label: BytesSystemConfig.bytesPow.TB,
  },
];
