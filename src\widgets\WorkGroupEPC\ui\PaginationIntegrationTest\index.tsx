import React, { useState, useCallback } from 'react';
import { Tree, Card, Button, Space, Typography, Alert, Spin } from 'antd';
import { EventDataNode } from 'antd/lib/tree';
import { NodeWithButtonsAndPagination } from '../NodeWithButtonsAndPagination';
import { useEPCTreeWithPagination } from '../../store/hooks';
import { ENABLE_PAGINATION, DEBUG_PAGINATION } from '../../config/constants';

const { Title, Text } = Typography;

interface PaginationIntegrationTestProps {
  /** ID кабинета для тестирования */
  cabinetId: string;
}

/**
 * Интеграционный тест для проверки работы пагинации в дереве EPC
 * Демонстрирует полную интеграцию всех компонентов пагинации
 */
export const PaginationIntegrationTest: React.FC<PaginationIntegrationTestProps> = ({
  cabinetId,
}) => {
  const [enablePagination, setEnablePagination] = useState(ENABLE_PAGINATION);
  const [testMode, setTestMode] = useState(false);

  // Заглушка для togglePopup
  const togglePopup = useCallback(() => {
    console.log('togglePopup called');
  }, []);

  // Используем хук с пагинацией
  const treeHookResult = useEPCTreeWithPagination(
    cabinetId,
    togglePopup,
    enablePagination
  );

  const {
    tree,
    treeLoad,
    treeExpand,
    treeRefs,
    pagination,
  } = treeHookResult;

  // Кастомный рендер узла с поддержкой пагинации
  const renderTreeNode = useCallback((nodeData: TreeElement) => {
    const paginationInfo = pagination.getPaginationInfo(nodeData.key);
    
    return (
      <NodeWithButtonsAndPagination
        node={nodeData}
        paginationInfo={paginationInfo}
        onLoadMore={(nodeKey) => {
          console.log(`Loading more for node: ${nodeKey}`);
          return pagination.loadMoreItems(nodeKey);
        }}
      />
    );
  }, [pagination]);

  // Обработчик загрузки данных узла
  const handleLoadData = useCallback(async (treeNode: EventDataNode<TreeElement>) => {
    if (DEBUG_PAGINATION) {
      console.log('Loading data for node:', treeNode.key, treeNode);
    }
    
    try {
      await treeLoad.onLoadData(treeNode);
    } catch (error) {
      console.error('Error loading node data:', error);
    }
  }, [treeLoad.onLoadData]);

  // Сброс пагинации для тестирования
  const handleResetPagination = useCallback(() => {
    pagination.resetPagination();
    console.log('Pagination state reset');
  }, [pagination]);

  // Переключение режима пагинации
  const handleTogglePagination = useCallback(() => {
    setEnablePagination(prev => !prev);
    console.log('Pagination toggled:', !enablePagination);
  }, [enablePagination]);

  if (tree.isPending) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>Загрузка дерева...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (tree.error) {
    return (
      <Card>
        <Alert
          message="Ошибка загрузки дерева"
          description={tree.error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>Интеграционный тест пагинации</Title>
      
      <Alert
        message="Тестовый компонент"
        description="Этот компонент демонстрирует полную интеграцию пагинации с деревом EPC. В продакшене он должен быть удален."
        type="info"
        showIcon
        style={{ marginBottom: '20px' }}
      />

      {/* Панель управления тестом */}
      <Card title="Управление тестом" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button 
            type={enablePagination ? 'primary' : 'default'}
            onClick={handleTogglePagination}
          >
            Пагинация: {enablePagination ? 'Включена' : 'Выключена'}
          </Button>
          
          <Button onClick={handleResetPagination}>
            Сбросить пагинацию
          </Button>
          
          <Button 
            type={testMode ? 'primary' : 'default'}
            onClick={() => setTestMode(prev => !prev)}
          >
            Режим отладки: {testMode ? 'Включен' : 'Выключен'}
          </Button>
          
          <Button onClick={() => tree.handleResetTree()}>
            Перезагрузить дерево
          </Button>
        </Space>
      </Card>

      {/* Информация о состоянии */}
      <Card title="Состояние дерева" style={{ marginBottom: '20px' }}>
        <Space direction="vertical">
          <Text>
            <Text strong>Загруженных узлов:</Text> {treeLoad.loadedKeys.length}
          </Text>
          <Text>
            <Text strong>Развернутых узлов:</Text> {treeExpand.expandedKeys.length}
          </Text>
          <Text>
            <Text strong>Всего узлов в дереве:</Text> {tree.data?.length || 0}
          </Text>
          <Text>
            <Text strong>Пагинация активна:</Text> {enablePagination ? 'Да' : 'Нет'}
          </Text>
        </Space>
      </Card>

      {/* Дерево с пагинацией */}
      <Card title="Дерево EPC с пагинацией">
        {tree.data && tree.data.length > 0 ? (
          <Tree
            ref={treeRefs.treeRef}
            treeData={tree.data}
            loadData={handleLoadData}
            loadedKeys={treeLoad.loadedKeys}
            expandedKeys={treeExpand.expandedKeys}
            autoExpandParent={treeExpand.autoExpandParent}
            onExpand={treeExpand.handleExpand}
            titleRender={renderTreeNode}
            showLine={{ showLeafIcon: false }}
            height={400}
            virtual
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">Нет данных для отображения</Text>
          </div>
        )}
      </Card>

      {/* Отладочная информация */}
      {testMode && (
        <Card title="Отладочная информация" style={{ marginTop: '20px' }}>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto',
            maxHeight: '300px'
          }}>
            {JSON.stringify({
              enablePagination,
              loadedKeysCount: treeLoad.loadedKeys.length,
              expandedKeysCount: treeExpand.expandedKeys.length,
              treeDataLength: tree.data?.length || 0,
              debugPagination: DEBUG_PAGINATION,
            }, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export default PaginationIntegrationTest;
