import { Switch, Typography } from 'antd';
import { FC } from 'react';
import {
  EPCDownloadModalInnerProps,
  WorkGroupEPCConfig,
  WorkGroupEPCStore,
  EPCDownloadModalProps,
} from 'widgets/WorkGroupEPC';
import { AppPopup, BorderedFieldset, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

const EPCDownloadModalInner: FC<EPCDownloadModalInnerProps> = ({
  settings,
  setSettings,
  handleClose,
  selectedFiles,
  selectedDirs,
  loadedKeys,
  cabinetId,
  resetCheckboxes,
}) => {
  const { downloadSettingsFields } = WorkGroupEPCConfig;

  const buttons = WorkGroupEPCStore.hooks.useDownloadButtons(
    selectedFiles,
    selectedDirs,
    loadedKeys,
    handleClose,
    cabinetId,
    settings,
    resetCheckboxes,
  );

  return (
    <>
      <BorderedFieldset
        title="Настройки скачивания"
        containerClassName={styles.container}
      >
        <div className={styles.content}>
          {downloadSettingsFields.map((item) => (
            <div className={styles.settings} key={item.key}>
              <Typography.Text>{item.title}:</Typography.Text>
              <Switch
                onChange={(checked) => {
                  setSettings({ ...settings, [item.key]: checked });
                }}
                checked={settings[item.key]}
                disabled={item.key === 'archive' && settings.unArchive}
              />
            </div>
          ))}
        </div>
      </BorderedFieldset>

      <ButtonsContainer buttons={buttons} />
    </>
  );
};

export const EPCDownloadModal: FC<EPCDownloadModalProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    className={styles.popup}
    isOpened={isOpened}
    onClose={handleClose}
    title="Скачать файлы с описью"
  >
    <EPCDownloadModalInner {...props} handleClose={handleClose} />
  </AppPopup>
);
