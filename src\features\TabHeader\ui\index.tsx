import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { FC } from 'react';
import { useToggle } from 'react-use';
import { TabHeaderProps } from 'features/TabHeader';
import { ApiContainer, ButtonsContainer } from 'shared/ui';
import { HeaderData } from './HeaderData';

import styles from './styles.module.scss';

export const TabHeader: FC<TabHeaderProps> = ({
  headerData,
  editCallback,
  isCanEdit,
  isPending,
  error,
  isOpened,
}) => {
  const [isHeaderFullSize, toggleHeaderFullSize] = useToggle(isOpened || false);

  return (
    <div className={styles.head}>
      <ApiContainer
        isPending={isPending || false}
        error={error || null}
        className={styles.spinner}
      >
        <div
          className={classNames(
            styles.container,
            styles[`container${isHeaderFullSize ? 'Full' : 'Small'}`],
          )}
        >
          <HeaderData
            headerData={headerData}
            editCallback={editCallback}
            isCanEdit={isCanEdit}
          />
        </div>

        <ButtonsContainer
          className={styles.headToggle}
          buttons={[
            {
              title: '',
              className: styles.headToggleButton,
              tooltip: !isHeaderFullSize ? 'Развернуть' : 'Свернуть',
              tooltipProps: {
                placement: 'bottom',
              },
              key: '1',
              onClick: () => toggleHeaderFullSize(!isHeaderFullSize),
              icon: isHeaderFullSize ? (
                <CaretUpOutlined />
              ) : (
                <CaretDownOutlined />
              ),
            },
          ]}
        />
      </ApiContainer>
    </div>
  );
};
