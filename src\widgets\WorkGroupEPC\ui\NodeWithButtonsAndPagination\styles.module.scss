.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.loadMoreContainer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 24px; // Отступ для выравнивания с дочерними элементами дерева
  margin-top: 4px;
  margin-bottom: 4px;
  padding-left: 8px;

  // Добавляем тонкую линию слева для визуальной связи с деревом
  border-left: 1px dashed #d9d9d9;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -1px;
    top: -4px;
    width: 12px;
    height: 1px;
    border-top: 1px dashed #d9d9d9;
  }
}

.loadMoreButton {
  // Дополнительные стили для кнопки в контексте дерева
  :global(.ant-btn) {
    border-radius: 4px;
    font-size: 11px;
    height: 22px;
    padding: 0 8px;

    &:hover {
      transform: none; // Отключаем трансформацию для дерева
    }
  }
}

// Адаптация для разных уровней вложенности дерева
.container[data-level="1"] .loadMoreContainer {
  margin-left: 48px;
}

.container[data-level="2"] .loadMoreContainer {
  margin-left: 72px;
}

.container[data-level="3"] .loadMoreContainer {
  margin-left: 96px;
}

// Темная тема
[data-theme='dark'] {
  .loadMoreContainer {
    border-left-color: #434343;

    &::before {
      border-top-color: #434343;
    }
  }
}

// Состояние загрузки
.loadMoreContainer:has(.loadMoreButton :global(.ant-btn-loading)) {
  opacity: 0.8;

  &::before {
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
