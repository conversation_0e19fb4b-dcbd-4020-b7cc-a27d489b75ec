import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import {
  filenetAttachmentsTreeLib,
  FileNetStructureTreeResponse,
  ParsedDossierStructureTree,
} from 'entities/FilenetAttachmentsTree';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { downloadFile } from 'shared/lib';

const ROOT_KEY = '0-0';

export const getStructureTreeThunk = createAsyncThunk<
  ParsedDossierStructureTree[],
  string,
  { state: import('processes/store').RootState }
>(
  'filenetDossierStructure/getStructureTreeThunk',
  async (popupId, { signal, rejectWithValue }) => {
    try {
      const source = axios.CancelToken.source();

      signal.addEventListener('abort', () => {
        source.cancel();
      });

      const { data } =
        await filenetServiceInstance.get<FileNetStructureTreeResponse>(
          apiUrls.fileNet.dossierStructure.tree(popupId),
          {
            cancelToken: source.token,
          },
        );

      return filenetAttachmentsTreeLib.parseDossierResTree(
        data.list || [],
        true,
        ROOT_KEY,
      );
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
);

export const getSubTreeThunk = createAsyncThunk<
  { key: Key; tree: ParsedDossierStructureTree[] },
  { cardId: string | number; key: Key; numericKey: Key },
  { state: import('processes/store').RootState }
>(
  'filenetDossierStructure/onLoadTreeDataThunk',
  async ({ cardId, key, numericKey }, { signal, rejectWithValue }) => {
    try {
      const source = axios.CancelToken.source();

      signal.addEventListener('abort', () => {
        source.cancel();
      });

      const { data } =
        await filenetServiceInstance.post<FileNetStructureTreeResponse>(
          apiUrls.fileNet.dossierStructure.subTree(cardId),
          { subTreePath: key },
          {
            cancelToken: source.token,
          },
        );

      return {
        tree: filenetAttachmentsTreeLib.parseDossierResTree(
          data.list || [],
          true,
          String(numericKey),
        ),
        key,
      };
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
);

export const getStructureFileThunk = createAsyncThunk<
  void,
  void,
  { state: import('processes/store').RootState }
>(
  'filenetDossierStructure/getStructureFileThunk',
  async (_, { rejectWithValue, getState }) => {
    try {
      const { checkedKeys } = getState().filenetDossierStructure;

      const { data } = await filenetServiceInstance.post<Blob>(
        apiUrls.fileNet.dossierStructure.file,
        {
          paths: checkedKeys,
          baseUrl: filenetServiceInstance.defaults.baseURL,
        },
        { responseType: 'blob' },
      );

      downloadFile(data, 'file-links.txt');
      return undefined;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }

      throw err;
    }
  },
);
