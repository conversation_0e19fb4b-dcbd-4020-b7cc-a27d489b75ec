export interface AuthDto {
  /** Состояние аутентификации */
  authenticated: boolean;
  name: string;
  principal: {
    attributes: {
      /** ФИО пользователя */
      displayName: string;
      /** Имя в лдапе */
      dn: string;
      email: string;
      username: string;
    };
    /** Principal name */
    name: string;
  };
}

export type UserInfoInitialState = ApiDefaultKeys & {
  auth: AuthDto | null;
};
