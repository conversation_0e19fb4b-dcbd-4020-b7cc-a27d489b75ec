import { useCallback, useLayoutEffect } from 'react';
import { WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAppDispatch, useCreateSliceActions } from 'shared/model';

export const useEPCActions = (
  cabinetId: string,
): [(body: MoveDNDBody) => Promise<string>, Callback] => {
  const dispatch = useAppDispatch();

  const { reset } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );

  const handleMove = useCallback(
    async (body: MoveDNDBody) => {
      const res = await dispatch(
        WorkGroupEPCStore.actions.postTreeMove({
          url: apiUrls.workGroup.EPC.saveMove,
          body,
        }),
      ).unwrap();

      return res;
    },
    [dispatch],
  );

  const getEPCTree = useCallback(() => {
    dispatch(
      WorkGroupEPCStore.actions.getEpcTreeThunk(
        generateUrlWithQueryParams(
          apiUrls.workGroup.EPC.getLazyTree,
          {
            cabinetId,
            user: 'admin',
            isCabinet: true,
          },
          { parseAllValues: true },
        ),
      ),
    );
  }, [cabinetId, dispatch]);

  useLayoutEffect(() => {
    getEPCTree();

    return () => reset();
  }, []); // eslint-disable-line

  return [handleMove, getEPCTree];
};
