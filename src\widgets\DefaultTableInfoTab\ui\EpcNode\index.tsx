import {
  DownloadOutlined,
  EyeOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { ReactNode } from 'react';
import { apiUrls, appInstance } from 'shared/api';
import {
  asyncDownloadFile,
  createNewWindowForApps,
  downloadFile,
  guessNoticeMessage,
} from 'shared/lib';
import { normalizeFileName } from 'shared/model';

import styles from './styles.module.scss';

const handleGetFile = async (endpoint: string): Promise<void> => {
  const res = await appInstance.get(endpoint, {
    responseType: 'blob',
  });
  downloadFile(res.data, normalizeFileName(res.headers));
};

export const epcNode = (title: ReactNode, node: TreeElement): ReactNode => (
  <div className={styles.title}>
    {title}
    <div title="" className={styles.buttonsContainer}>
      {node.fileNetId && node.permissions?.canDownload && (
        <Tooltip title="Скачать файл">
          <DownloadOutlined
            className={styles.buttonsButton}
            onClick={() => {
              if (node.fileNetId) {
                asyncDownloadFile(
                  handleGetFile(
                    apiUrls.workGroup.fileData.downloadFileById(node.fileNetId),
                  ),
                );
              }
            }}
          />
        </Tooltip>
      )}
      {node.fileSignExist && node.fileNetId && node.permissions?.canDownload && (
        <Tooltip title="Скачать файл с подписью">
          <SafetyOutlined
            className={styles.buttonsButton}
            onClick={() => {
              if (node.fileNetId) {
                asyncDownloadFile(
                  handleGetFile(
                    apiUrls.workGroup.fileData.downloadFileByIdWithSign(
                      node.fileNetId,
                    ),
                  ),
                );
              }
            }}
          />
        </Tooltip>
      )}

      {node.fileNetId && node.permissions?.canView && (
        <Tooltip title="Просмотр в визуализаторе">
          <EyeOutlined
            className={styles.buttonsButton}
            onClick={async () => {
              if (node.fileNetId) {
                const res = await appInstance.get<{
                  data: string;
                  success: boolean;
                }>(apiUrls.workGroup.fileData.fileViewer(node.fileNetId));
                guessNoticeMessage('viewer');
                createNewWindowForApps(res.data.data);
              }
            }}
          />
        </Tooltip>
      )}
    </div>
  </div>
);
