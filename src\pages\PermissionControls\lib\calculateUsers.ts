import moment from 'moment/moment';
import { PermissionInitialState } from 'pages/PermissionControls/types';
import { UserConfig, UserProfileResponse } from 'widgets/PermissionModals';
import { TableRowData } from 'features/DataGrid';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';

export const calculateUsers = (
  rows: TableRowData[],
): PermissionInitialState['alertPopups'] => {
  const deletedUserRows: TableRowData[] = [];
  const lockedUserRows: TableRowData[] = [];
  const newUserRows: TableRowData[] = [];
  const expiredUserRows: TableRowData[] = [];

  rows.forEach((row) => {
    if (!row?.checkboxStatus) return;
    if (!row?.profiles || row?.profiles?.length === 0) return;
    if (!row?.rowId) return;

    // eslint-disable-next-line
    const profiles: UserProfileResponse = row.profiles;
    const userProperties = (
      profiles[0].type === 'user_profile_login_properties'
        ? profiles[0]
        : profiles[1]
    ) as UserConfig;
    const { isDeleted, isBlocked, isActive } = row.checkboxStatus;

    if (row.rowId?.isFirstActivate && !isActive) {
      newUserRows.push(row);
    }

    if (isDeleted && isActive) {
      deletedUserRows.push(row);
    }

    if (isBlocked && isActive) {
      lockedUserRows.push(row);
    }

    if (
      !userProperties?.notExpired &&
      userProperties?.validUntil &&
      moment(userProperties.validUntil, DEFAULT_DATE_VARIANT).isBefore(
        moment(),
      ) &&
      isActive
    ) {
      expiredUserRows.push(row);
    }
  });

  return {
    deletedUserRows,
    expiredUserRows,
    lockedUserRows,
    newUserRows,
  };
};
