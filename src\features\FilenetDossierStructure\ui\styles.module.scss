@import 'src/app/styles/mixins';

.container {
  height: 70vh;
  width: 50vw;
  min-width: 800px;
  margin: auto;
  border-radius: 5px;
  transition: height, width 0.1s ease-in;
  z-index: 1000;
}

.fullSize {
  height: 95vh;
  width: 70vw;
}

.treeContainer {
  padding: 10px;
  @include defaultBorder;

  margin-bottom: 10px;
  width: 100%;
  height: 100%;

  :global(.ant-spin-container) {
    height: 100% !important;
    overflow: hidden !important;
  }
}

.autoSizer {
  height: 100%;
  width: 100%;
}
