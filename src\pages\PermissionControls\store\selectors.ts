import { createSelector } from '@reduxjs/toolkit';
import { permissionModalsConfig } from 'widgets/PermissionModals';
import { selectSelf } from 'shared/lib';

const {
  enums: { PermissionTabNames },
} = permissionModalsConfig;

const selfSelector = selectSelf('newPermissionControls');

export const tabsSelector = createSelector(selfSelector, (state) => state.tabs);

export const usersTabSelector = createSelector(
  tabsSelector,
  (state) => state[PermissionTabNames.Users],
);
export const groupsTabSelector = createSelector(
  tabsSelector,
  (state) => state[PermissionTabNames.Groups],
);
export const rolesTabSelector = createSelector(
  tabsSelector,
  (state) => state[PermissionTabNames.Roles],
);

export const alertPopupsSelector = createSelector(
  selfSelector,
  (state) => state.alertPopups,
);
