import { parseBlitzUrlToLogout } from '../lib';

describe('parseBlitzUrlToLogout', () => {
  const CORRECT_END = '/blitz/login/logout';

  it('Должен отпарсить URL в нужный формат, если URL содержит /blitz', () => {
    const correctUrls = [
      '/blitz',
      'sabah/blitz',
      '/blitz/',
      '/blitz/hamse',
      '/blitz',
    ];

    correctUrls.forEach((url) => {
      expect(parseBlitzUrlToLogout(url).endsWith(CORRECT_END)).toBeTruthy();
    });
  });

  it('Должен быбросить ошибку если ендпоинт не содержит /blitz', () => {
    const wrongUrls = ['', '/blizz', 'blitz', '/neBlitz', '/bblitz'];

    wrongUrls.forEach((url) => {
      expect(() => parseBlitzUrlToLogout(url)).toThrow(TypeError);
    });
  });
});
