import { DownOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';

import styles from './styles.module.scss';

export interface LoadMoreButtonProps {
  /** Колбэк для загрузки дополнительных элементов */
  onLoadMore: () => void;
  /** Текст кнопки (по умолчанию "Загрузить еще") */
  children?: React.ReactNode;
  /** Дополнительные CSS классы */
  className?: string;
  /** Отключена ли кнопка */
  disabled?: boolean;
  /** Количество уже загруженных элементов */
  loadedCount?: number;
  /** Состояние загрузки */
  loading?: boolean;
  /** Показывать ли счетчик элементов */
  showCounter?: boolean;
  /** Размер кнопки */
  size?: 'small' | 'middle' | 'large';
  /** Общее количество элементов (если известно) */
  totalCount?: number;
  /** Тип отображения кнопки */
  variant?: 'default' | 'compact' | 'minimal';
}

/**
 * Компонент кнопки "Загрузить еще" для пагинации Используется в деревьях и
 * списках для инкрементальной загрузки данных
 */
export const LoadMoreButton: FC<LoadMoreButtonProps> = ({
  onLoadMore,
  loading = false,
  disabled = false,
  loadedCount,
  totalCount,
  size = 'small',
  className,
  children,
  showCounter = true,
  variant = 'default',
}) => {
  const getButtonText = () => {
    if (children) {
      return children;
    }

    if (variant === 'minimal') {
      return '...';
    }

    if (variant === 'compact') {
      return 'Еще';
    }

    return 'Загрузить еще';
  };

  const getTooltipText = () => {
    if (loading) {
      return 'Загрузка...';
    }

    if (disabled) {
      return 'Загрузка недоступна';
    }

    if (showCounter && loadedCount !== undefined) {
      if (totalCount !== undefined) {
        return `Загружено ${loadedCount} из ${totalCount} элементов`;
      }
      return `Загружено ${loadedCount} элементов`;
    }

    return 'Загрузить дополнительные элементы';
  };

  const getCounterText = () => {
    if (!showCounter || loadedCount === undefined) {
      return null;
    }

    if (totalCount !== undefined) {
      return ` (${loadedCount}/${totalCount})`;
    }

    return ` (${loadedCount})`;
  };

  const buttonContent = (
    <>
      {loading ? (
        <LoadingOutlined className={styles.icon} />
      ) : (
        <DownOutlined className={styles.icon} />
      )}
      <span className={styles.text}>
        {getButtonText()}
        {getCounterText()}
      </span>
    </>
  );

  return (
    <div className={classNames(styles.container, styles[variant], className)}>
      <Tooltip title={getTooltipText()} placement="top">
        <Button
          type="dashed"
          size={size}
          loading={loading}
          disabled={disabled}
          onClick={onLoadMore}
          className={classNames(styles.button, {
            [styles.loading]: loading,
            [styles.disabled]: disabled,
          })}
          icon={
            variant === 'minimal' ? undefined : loading ? (
              <LoadingOutlined />
            ) : (
              <DownOutlined />
            )
          }
        >
          {variant === 'minimal' ? (
            getButtonText()
          ) : (
            <>
              {getButtonText()}
              {getCounterText()}
            </>
          )}
        </Button>
      </Tooltip>
    </div>
  );
};

export default LoadMoreButton;
