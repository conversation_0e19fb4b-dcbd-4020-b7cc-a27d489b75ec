import { FileProtectOutlined, RedoOutlined } from '@ant-design/icons';
import { Divider } from 'antd';
import { FC, useCallback, useLayoutEffect, useMemo } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { DIGIT_PATTERN } from 'shared/config/constants';

import { useSearchParamsState } from 'shared/model';
import { useAxiosRequest } from 'shared/model/useAxiosRequest';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { PageContainer } from 'shared/ui/PageContainer';

import type { FNRResponse, StepperItem } from '..';
import { columns } from '../model';
import { reducers } from '../store';

import { FileTypes } from './FileTypes';
import { MaterialIdenticatorFinder } from './MaterialIdenticatorFinder';
import { OrganizationalObjects } from './OrganizationalObjects';
import { OrgStructureModal } from './OrgStructureModal';
import { ReportType } from './ReportType';
import styles from './styles.module.scss';
import { UserModal } from './UserModal';
import { UserModalInputs } from './UserModalInputs';
import { WorkModal } from './WorkModal';
import { WorkModalInputs } from './WorkModalInputs';

const steps: StepperItem[] = [
  {
    title: 'Фильтры',
    key: 'filters',
    element: (
      <>
        <ReportType type="workModal" />
        <WorkModalInputs />
      </>
    ),
  },
  {
    title: 'Идентификаторы',
    key: 'ids',
    element: <MaterialIdenticatorFinder />,
  },
  {
    title: 'Организационные объекты',
    key: 'objects',
    element: <OrganizationalObjects />,
  },
  {
    title: 'Типы файлов',
    key: 'ids',
    element: <FileTypes />,
  },
];

/** Страница отчетов "Работа пользователей с ФХ" */
export const FileNetReport: FC = () => {
  const [page, setPage] = useSearchParamsState('page', '1', DIGIT_PATTERN);

  const [dataTableTrigger, dataTableStatuses] = useAxiosRequest<FNRResponse>(
    filenetServiceInstance,
  );

  const { openWorkModal, openUsersModal } = useCreateSliceActions(
    reducers.slice.actions,
  );

  const getTableData = useCallback((newPage) => {
    dataTableTrigger(apiUrls.fileNet.report.table, {
      method: 'POST',
      data: {
        first: (newPage - 1) * 10,
        rows: 10,
        sortField: 'creationDate',
        sortOrder: '-1',
        sortMeta: [],
      },
    });
  }, []); // eslint-disable-line

  const rows = useMemo(
    () =>
      Array.isArray(dataTableStatuses?.data?.data)
        ? (dataTableStatuses?.data?.data.map((row) => ({
            ...row,
            key: row.id,
          })) as TableRowData[])
        : [],
    [dataTableStatuses?.data?.data],
  );

  useLayoutEffect(() => {
    getTableData(page);
  }, []); // eslint-disable-line

  return (
    <PageContainer containerKey="FileNetReport">
      <AppHeader title="Отчет 'Работа пользователей с ФХ'" />

      <ButtonsContainer
        className={styles.container}
        buttons={[
          {
            key: 'usersModal',
            title: "Настроить отчет 'Пользователи системы'",
            tooltip: 'Открыть модальное окно для настройки отчета',
            onClick: () => openUsersModal(),
            type: 'primary',
            icon: <FileProtectOutlined />,
          },
          {
            key: 'workModal',
            title: "Настроить отчет 'Работа пользователей с ФХ'",
            tooltip: 'Открыть модальное окно для настройки отчета',
            onClick: () => openWorkModal(),
            type: 'primary',
            icon: <FileProtectOutlined />,
          },
        ]}
      />
      <Divider />

      <DataGrid
        additionalClassNames={{
          container: styles.container,
          buttonsContainer: styles.buttonsContainer,
        }}
        additionalButtons={[
          {
            key: 'refresh',
            title: 'Обновить',
            type: 'primary',
            icon: <RedoOutlined />,
            ghost: true,
            onClick: () => getTableData(page ?? 1),
          },
        ]}
        columns={columns}
        rows={rows}
        hideColumnSearch
        hideSorter
        tableAdditionProps={{
          loading: !dataTableStatuses.isResEnd && dataTableStatuses.isPending,
          scroll: { x: '100%', y: '65vh' },
          size: 'small',
          pagination: {
            position: ['bottomLeft'],
            total: dataTableStatuses?.data?.totalRecords ?? 0,
            hideOnSinglePage: true,
            pageSize: 10,
            showSizeChanger: false,
            current: page ? parseInt(page, 10) : 1,
            onChange: (newPage) => {
              setPage(String(newPage));
              getTableData(newPage);
            },
          },
        }}
      />

      <UserModal>
        <ReportType type="usersModal" />
        <UserModalInputs />
      </UserModal>

      <WorkModal steps={steps} />

      <OrgStructureModal />
    </PageContainer>
  );
};
