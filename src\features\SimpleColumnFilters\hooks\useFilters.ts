import { useState } from 'react';
// eslint-disable-next-line boundaries/element-types
import {
  ColumnFilters,
  customColumnFilters,
} from 'features/CustomColumnFilters';
// eslint-disable-next-line boundaries/element-types
import { TableColumnData } from 'features/DataGrid';
import { FetchFilteredData, FiltersInitial } from '../types';
import { useColumnFilters } from './useColumnFilters';

type SimpleFiltersHookResult = {
  filters: ColumnFilters[];
  getFilterProps: (column: TableColumnData) => Partial<TableColumnData>;
  setFilters: React.Dispatch<React.SetStateAction<FiltersInitial>>;
};

export const useFilters = ({
  fetchFilteredData,
}: {
  fetchFilteredData: FetchFilteredData;
}): SimpleFiltersHookResult => {
  const [
    handleFilterSubmit,
    activeFilters,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _resetFilters,
    handleFilterReset,
    setFilters,
  ] = useColumnFilters({
    fetchFilteredData,
  });
  const [activeFilterDropdownId, setActiveFilterDropdownId] = useState('');

  const getFilterProps = (
    column: TableColumnData,
  ): Partial<TableColumnData> => ({
    ...(Object.hasOwn(column, 'filterType') &&
      column?.filterType !== null && {
        filters: [],
        filteredValue: [],
        onFilterDropdownOpenChange: (isOpen) =>
          isOpen
            ? setActiveFilterDropdownId(column.dataIndex)
            : setActiveFilterDropdownId(''),
        filterDropdownOpen: column.dataIndex === activeFilterDropdownId,
        filterDropdown: () =>
          customColumnFilters(
            column,
            (filterValue) => {
              setActiveFilterDropdownId('');
              handleFilterSubmit?.(filterValue);
            },
            activeFilters,
            (filterValue) => {
              setActiveFilterDropdownId('');
              handleFilterReset?.(filterValue);
            },
          ),
      }),

    filtered: activeFilters.some(
      (filterItem) => filterItem.column === column.dataIndex,
    ),
  });

  return {
    filters: activeFilters,
    getFilterProps,
    setFilters,
  };
};
