import { FC } from 'react';
import {
  EpcPermissions,
  WorkGroupEPCConfig,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { permissionsConfig } from 'entities/Permissions';
import { useAppSelector } from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { CardsLink } from '../CardsLink';

import styles from './styles.module.scss';

interface WatchModalProps {
  epcPermissions: EpcPermissions;
  handleClose: Callback;
  handleLinkClick: (isDirectory: boolean, itemId: string) => void;
  isLinkedReqEnd: boolean;
  setIsLinkedReqEnd: React.Dispatch<React.SetStateAction<boolean>>;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
}

export const WatchModal: FC<WatchModalProps> = ({
  isLinkedReqEnd,
  handleClose,
  togglePopup,
  handleLinkClick,
  epcPermissions,
  setIsLinkedReqEnd,
}) => {
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  return (
    <AppPopup
      isOpened
      className={styles.popup}
      onClose={handleClose}
      title={`Список ярлыков для "${epc.linkedData.title}"`}
    >
      <CardsLink
        isLinkedReqEnd={isLinkedReqEnd}
        setIsLinkedReqEnd={setIsLinkedReqEnd}
        linked={epc.linkedData.watchLinked}
        handleCardClick={handleLinkClick}
        handleClose={handleClose}
        hoverable
      />

      <ButtonsContainer
        buttons={[
          {
            title: 'Редактировать привязки',
            disabled: !epcPermissions.canEdit,
            tooltip: !epcPermissions.canEdit
              ? permissionsConfig.warnMessages.noPermissionsDefault(
                  'редактирование',
                )
              : undefined,
            key: 'edit',
            onClick: () => togglePopup('edit'),
          },
        ]}
      />
    </AppPopup>
  );
};
