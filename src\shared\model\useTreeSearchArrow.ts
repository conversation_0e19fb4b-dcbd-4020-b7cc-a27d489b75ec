import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getFilteredFlatTreeByParam } from 'shared/lib';

type UseTreeSearchArrow = [
  typeof useRef,
  TreeElement[],
  number,
  Key,
  Callback,
  Callback,
];

/**
 * Фукнция для работы с найдеными значениями в дереве. Для работы функции нужно
 * взять реф дерева. Для дерева указать параметр height={число}. Так же в дереве
 * нужно в параметр titleRender обязательно дать булево значение через проверку
 * ключа ноды и найденого ключа в рендер тайтла проверив длинну массива tagged.
 * В кнопки указать колбеки верха и низа. Отрисовка кнопок рекомендуется после
 * проверки массива tagged на длинну.
 *
 * @param treeArr - Массив дерева по которому будет выполнен поиск, query -
 *   строка поиска
 * @param query - Поисковой запрос
 * @param autoExpand - Колбек автоэкспанда дерева, берется из хука
 *   useExpandTreeActions
 * @returns Массив [реф дерева, плоский массив найденых элементов, индекс
 *   элемента, найденый ключ дерева , колбек кнопки вверх, колбек кнопки вниз]
 * @link renderTreeTitle добавить функцию рендера тайтла или расширеную версию рендера тайтла с контекст меню.
 */

export const useTreeSearchArrow = (
  treeArr: TreeElement[],
  query: string,
  autoExpand: Callback,
): UseTreeSearchArrow => {
  const treeRef = useRef(null);
  const [currentTreeIndex, setCurrentTreeIndex] = useState(-1);

  const tagged = useMemo(() => getFilteredFlatTreeByParam(treeArr, 'tagged').filter(
      (i) => i.tagged,
    ), [treeArr]);

  const handleSetArrowDown = useCallback(() => {
    autoExpand();
    setCurrentTreeIndex((prev) => {
      const newValue = prev + 1;

      return newValue >= tagged.length ? 0 : newValue;
    });
  }, [tagged]); //eslint-disable-line

  const handleSetArrowUp = useCallback(() => {
    autoExpand();
    setCurrentTreeIndex((prev) => {
      const newValue = prev - 1;

      return newValue < 0 ? tagged.length - 1 : newValue;
    });
  }, [tagged]); //eslint-disable-line

  useEffect(() => {
    if (treeRef.current && 'scrollTo' in treeRef.current && tagged.length > 0) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      treeRef.current?.scrollTo({
        key: tagged[currentTreeIndex]?.key,
        align: 'top',
      });
    }
  }, [currentTreeIndex, tagged]);

  useEffect(() => {
    if (query === '') {
      setCurrentTreeIndex(0);
    }
  }, [query]);

  return [
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    treeRef,
    tagged,
    currentTreeIndex + 1,
    tagged.length !== 0 ? tagged[currentTreeIndex]?.key : '',
    handleSetArrowUp,
    handleSetArrowDown,
  ];
};
