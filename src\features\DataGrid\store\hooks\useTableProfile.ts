import { useCallback, useEffect, useState } from 'react';
import type {
  TableColumnData,
  TableConfig,
  TableSizes,
} from 'features/DataGrid';
import { DEFAULT_PROFILE_NAME } from 'shared/config/constants';
import { PresetTypes } from 'shared/config/enums';
import { usePreset } from 'shared/model/hooks';
import { useResizeStore } from './useResizeStore';

type OnResize = (newTableSizes: TableSizes) => void;
type OnChange = (newColumns: TableColumnData[]) => void;
type OnProfileSet = (profile: TableConfig) => void;

export const useTableProfile = (
  columns: TableColumnData[],
  tableName: string,
): {
  currentSelectedProfile: TableConfig | null;
  isPending: boolean;
  onChange: OnChange;
  onProfileSet: OnProfileSet;
  onResize: OnResize;
  tableSizes: TableSizes;
  updatedColumns: TableColumnData[];
} => {
  const [isCached, setIsCached] = useState(false);
  const [updatedColumns, setUpdatedColumns] = useState<TableColumnData[]>([]);
  const [currentSelectedProfile, setCurrentSelectedProfile] = useState<Record<
    string,
    TableConfig
  > | null>(null);

  const { presetCodeStatuses, get, statuses } = usePreset<TableConfig>(
    tableName,
    PresetTypes.TABLE_CONFIG,
  );

  const [tableSizes, setTableSizes] = useResizeStore(tableName, columns);

  /** Колбек на ресайз */
  const onResize = useCallback<OnResize>(
    (newTableSizes) => {
      setTableSizes(newTableSizes);
    },
    [setTableSizes],
  );

  /** Колбэк на обновление стейта */
  const onChange = useCallback<OnChange>((newColumns: TableColumnData[]) => {
    setUpdatedColumns(newColumns);
  }, []);

  /** Колбек на селект профиля */
  const onProfileSet = useCallback<OnProfileSet>(
    (profile) => {
      setCurrentSelectedProfile((prev) => ({ ...prev, [tableName]: profile }));
      const map = new Map<string, number>();

      columns.forEach(({ key }, index) => {
        map.set(key as string, index);
      });

      setUpdatedColumns(
        profile.asColumns.reduce((acc, savedColumn) => {
          if (map.has(savedColumn.key as string)) {
            acc.push({
              ...columns[map.get(savedColumn.key as string) as number],
              width: savedColumn.width,
            });
          }

          return acc;
        }, [] as TableColumnData[]),
      );
    },
    [columns, tableName],
  );

  /** Получение профиля по умолчанию */
  useEffect(() => {
    if (!isCached && !statuses.isPending && columns.length > 0) {
      if (presetCodeStatuses?.data?.includes(DEFAULT_PROFILE_NAME)) {
        get(DEFAULT_PROFILE_NAME).then((profile) => {
          if (profile.asColumns.length > 0) {
            onProfileSet(profile);
            setUpdatedColumns(profile.asColumns);
            setCurrentSelectedProfile((prev) => ({
              ...prev,
              [tableName]: profile,
            }));
            setIsCached(true);
          }
        });
      } else if (!statuses.isPending) {
        /**
         * Если профиль с именем DEFAULT_PROFILE_NAME отсутствует, обновляем
         * состояние приложения и устанавливаем признак кэширования в `true`
         */
        if (
          currentSelectedProfile &&
          currentSelectedProfile[tableName] &&
          columns.length > 0
        ) {
          /** Если есть активный профиль, устанавливаем его */
          onProfileSet(currentSelectedProfile[tableName]);
          setIsCached(true);
        } else {
          /** Если профиля нет, устанавливаем дефолтные колонки */
          setUpdatedColumns(columns);
          if (columns.length > 0) {
            setIsCached(true);
          }
        }
      }
    } else if (columns.length === 0) {
      setUpdatedColumns([]);
    }
  }, [
    columns,
    currentSelectedProfile,
    get,
    isCached,
    onProfileSet,
    presetCodeStatuses,
    presetCodeStatuses?.data,
    statuses.isPending,
    tableName,
  ]);

  useEffect(() => {
    setIsCached(false);
  }, [tableName]); // eslint-disable-line

  /** Сброс колонок при анмаунте */
  useEffect(
    () => () => {
      setUpdatedColumns([]);
      setIsCached(false);
    },
    [],
  ); // eslint-disable-line

  return {
    updatedColumns,
    onChange,
    tableSizes,
    onResize,
    isPending: presetCodeStatuses.isPending,
    onProfileSet,
    currentSelectedProfile: currentSelectedProfile
      ? currentSelectedProfile[tableName]
      : null,
  };
};
