import type { FC } from 'react';
import { useMemo, useState } from 'react';
import { useMeasure } from 'react-use';
import AutoSizer from 'react-virtualized-auto-sizer';
import {
  WorkGroupEPCStore,
  WorkGroupEPCConfig,
  WorkGroupEPCProps,
  EPCInnerProps,
  EpcPermissions,
} from 'widgets/WorkGroupEPC';
import { SearchDrawer } from 'features/SearchDrawer';

import { permissionsStore } from 'entities/Permissions';
import { useCreateSliceActions, usePopupsToggle } from 'shared/model';
import { AppPopup, ButtonsContainer } from 'shared/ui';
import { EPCTree } from './EPCTree';
import { Modals } from './Modals';
import styles from './styles.module.scss';
import { TreeSearch } from './TreeSearch';

const EPCInner: FC<EPCInnerProps> = (props) => {
  const { cabinetId, permissions, isFullSize, height, isFullFilesAccess } =
    props;

  const epcPermissions: EpcPermissions = useMemo(
    () => ({
      canViewFiles: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.VIEW_FILES_AT,
      ),
      canDownloadFiles: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.DOWNLOAD_FILES_AT,
      ),
      canEdit: permissions.ap_FilesEPC.includes(
        permissionsStore.enums.Actions.EDIT_LIST_AT,
      ),
      canDeleteFiles: permissions.ap_FilesWGO.includes(
        // APPID-2131
        permissionsStore.enums.Actions.DELETE_FILES_AT,
      ),
      canEditEPCStructure: permissions.ap_StructureEPC.includes(
        permissionsStore.enums.Actions.EDIT_LIST_AT,
      ),
      canCopyFiles:
        isFullFilesAccess &&
        permissions.ap_FilesEPC.includes(
          permissionsStore.enums.Actions.EDIT_LIST_AT,
        ),
    }),
    [isFullFilesAccess, permissions],
  );

  const { formFields, popupsInitial } = WorkGroupEPCConfig;

  const additionalSelects = WorkGroupEPCStore.hooks.useGetSearchSelects();
  const [statuses, saveStatus] =
    WorkGroupEPCStore.hooks.useVisibilityFileChange();

  const [popup, togglePopup] = usePopupsToggle(popupsInitial);

  const { handleResetSearchTags } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );

  // Условно используем пагинацию или обычное дерево
  const treeParams = WorkGroupEPCConfig.ENABLE_PAGINATION
    ? WorkGroupEPCStore.hooks.useEPCTreeWithPagination(cabinetId, togglePopup)
    : WorkGroupEPCStore.hooks.useEPCTree(cabinetId, togglePopup);

  // Извлекаем параметры пагинации, если они доступны
  const paginationParams = WorkGroupEPCConfig.ENABLE_PAGINATION && 'pagination' in treeParams
    ? (treeParams as any).pagination
    : undefined;
  const {
    treeDrag,
    treeSearch,
    treeActions,
    treeCheck,
    treeExpand,
    treeArrow,
  } = treeParams;

  const buttons = WorkGroupEPCStore.hooks.useButtons(
    treeCheck.checked.nodes,
    treeCheck.checked.selectedFiles,
    treeCheck.checked.selectedDirs,
    treeParams.tree.handleResetTree,
    togglePopup,
    epcPermissions,
    treeExpand.handleExpand,
    treeActions.refetchNode,
  );

  const [values, setValues] = useState<Record<string, string>>({});
  const [refButtons, { height: buttonsHeight }] = useMeasure<HTMLDivElement>();
  const treeHeight = height - buttonsHeight;

  const handleResetSearch = treeArrow.tagged.length
    ? handleResetSearchTags
    : undefined;

  return (
    <div className={styles.epcInner} style={{ height }}>
      <EPCTree
        isFullSize={isFullSize}
        treeHeight={treeHeight}
        epcPermissions={epcPermissions}
        togglePopup={togglePopup}
        saveStatus={saveStatus}
        {...treeParams}
        // Передаем параметры пагинации, если они доступны
        pagination={paginationParams}
      />

      <div className={styles.buttons} ref={refButtons}>
        <ButtonsContainer buttons={buttons} />
        <TreeSearch
          togglePopup={togglePopup}
          treeSearchConfig={{
            arrowUp: treeArrow.arrowUp,
            arrowDown: treeArrow.arrowDown,
            tagged: treeArrow.tagged,
            currentIndex: treeArrow.currentIndex,
          }}
        />
      </div>

      <SearchDrawer
        placement="right"
        width={550}
        handleClose={() => togglePopup('search')}
        isOpened={popup.search}
        handleResetSearch={handleResetSearch}
        formFields={[...formFields, ...additionalSelects]}
        values={values}
        setValues={setValues}
        searchCallback={async (body) => {
          if ('marks' in body) {
            await treeSearch.handleSearch({ ...body, isMainFile: true });
          } else {
            await treeSearch.handleSearch(body);
          }
          treeDrag.setIsDrag(false);
        }}
      />

      <Modals
        cabinetId={cabinetId}
        epcPermissions={epcPermissions}
        popup={popup}
        togglePopup={togglePopup}
        statuses={statuses}
        saveStatus={saveStatus}
        {...treeParams}
      />
    </div>
  );
};

export const WorkGroupEPC: FC<WorkGroupEPCProps> = ({
  handleClose,
  isOpened,
  ...props
}) => {
  const [isFullSize, setIsFullSize] = useState(false);
  return (
    <AppPopup
      fullSizeClassName={styles.popupFull}
      additionalFullSizeHandler={setIsFullSize}
      isOpened={isOpened}
      onClose={handleClose}
      className={styles.popup}
      title="ЭПП"
    >
      <AutoSizer disableWidth className={styles.autosizer}>
        {({ height }) => (
          <EPCInner {...props} isFullSize={isFullSize} height={height} />
        )}
      </AutoSizer>
    </AppPopup>
  );
};

// Экспорт всех UI компонентов
export * from './components';
