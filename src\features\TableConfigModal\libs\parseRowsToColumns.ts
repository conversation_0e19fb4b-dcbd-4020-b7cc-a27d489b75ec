type TableColumnData = import('features/DataGrid').TableColumnData;
type TableRowData = import('features/DataGrid').TableRowData;

/**
 * Конвертирует строки таблицы в столбцы с учетом указанных колонок и сортировки
 *
 * @param rows - Данные строк таблицы
 * @param columns - Данные колонок таблицы
 * @returns Массив объектов типа TableColumnData
 */
export const parseRowsToColumns = (
  rows: TableRowData[],
  columns: TableColumnData[],
): TableColumnData[] =>
  rows
    .filter((row: TableRowData) => row.show)
    .sort(
      (a: TableRowData, b: TableRowData) =>
        Number(a.position) - Number(b.position),
    )
    .reduce((acc: TableColumnData[], row: TableRowData) => {
      const column = columns.find((col) => col.key === row.key);

      if (column) {
        acc.push({
          ...JSON.parse(JSON.stringify(column)),
          width: Number(row.width),
        });
      }

      return acc;
    }, []);
