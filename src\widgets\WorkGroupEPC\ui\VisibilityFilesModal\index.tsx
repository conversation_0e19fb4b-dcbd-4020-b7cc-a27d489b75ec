import { Checkbox, Typography } from 'antd';
import { FC, useState } from 'react';
import { FilesVisibility, WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { AppPopup, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

interface VisibilityFilesModalInnerProps {
  checkedNodes: TreeElement[];
  handleClose: Callback;
  handleSave: FilesVisibility;
  isVisibilityPending: boolean;
  refetchNode: ({ itemId, isDirectory, key }: TreeElement) => Promise<void>;
  refetchTree: Callback;
}

type VisibilityFilesModalProps = VisibilityFilesModalInnerProps & {
  isOpened: boolean;
};

const VisibilityFilesModalInner: FC<VisibilityFilesModalInnerProps> = ({
  handleClose,
  checkedNodes,
  handleSave,
  isVisibilityPending,
  refetchTree,
  refetchNode,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const buttons = WorkGroupEPCStore.hooks.useVisibilityButtons(
    checkedNodes,
    isVisible,
    handleClose,
    handleSave,
    isVisibilityPending,
    refetchTree,
    refetchNode,
  );

  return (
    <div className={styles.content}>
      <div className={styles.action}>
        <Typography.Text>
          Элементы скрыты для Контроля выполнения
        </Typography.Text>
        <Checkbox
          checked={isVisible}
          onChange={(e) => setIsVisible(e.target.checked)}
        />
      </div>

      <ButtonsContainer buttons={buttons} />
    </div>
  );
};

export const VisibilityFilesModal: FC<VisibilityFilesModalProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    className={styles.popup}
    isOpened={isOpened}
    onClose={handleClose}
    title="Доступность элементов"
  >
    <VisibilityFilesModalInner handleClose={handleClose} {...props} />
  </AppPopup>
);
