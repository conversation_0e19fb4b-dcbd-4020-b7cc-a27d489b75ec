/**
 * Базовый тест функциональности пагинации
 * Проверяет минимально необходимые функции для работы кнопки "Загрузить еще"
 */

import { shouldUsePaginationForNode } from '../lib/paginationConfigHelper';
import { createPaginatedTreeUrl, hasMoreItems } from 'shared/lib';
import { PAGINATION_THRESHOLD } from '../config/constants';

// Мок данные для тестирования
const mockNode: TreeElement = {
  key: 'test-node-1',
  title: 'Test Directory',
  itemId: 'item-123',
  isDirectory: true,
  totalCountOfLeafs: 20,
  childrenCount: 15,
  isLeaf: false,
  isCallable: true,
  checked: false,
  disabled: false,
  isDisabled: false,
  children: [],
};

const mockSmallNode: TreeElement = {
  key: 'test-node-2',
  title: 'Small Directory',
  itemId: 'item-456',
  isDirectory: true,
  totalCountOfLeafs: 5,
  childrenCount: 3,
  isLeaf: false,
  isCallable: true,
  checked: false,
  disabled: false,
  isDisabled: false,
  children: [],
};

const mockFileNode: TreeElement = {
  key: 'test-file-1',
  title: 'Test File',
  itemId: 'file-789',
  isDirectory: false,
  totalCountOfLeafs: 0,
  childrenCount: 0,
  isLeaf: true,
  isCallable: true,
  checked: false,
  disabled: false,
  isDisabled: false,
  children: [],
};

/**
 * Тест функции определения необходимости пагинации
 */
export const testShouldUsePagination = (): boolean => {
  console.log('🧪 Тестирование shouldUsePaginationForNode...');
  
  // Тест 1: Директория с большим количеством файлов должна использовать пагинацию
  const shouldPaginateLarge = shouldUsePaginationForNode(mockNode);
  if (!shouldPaginateLarge) {
    console.error('❌ Ошибка: Большая директория должна использовать пагинацию');
    return false;
  }
  
  // Тест 2: Директория с малым количеством файлов не должна использовать пагинацию
  const shouldPaginateSmall = shouldUsePaginationForNode(mockSmallNode);
  if (shouldPaginateSmall) {
    console.error('❌ Ошибка: Маленькая директория не должна использовать пагинацию');
    return false;
  }
  
  // Тест 3: Файлы не должны использовать пагинацию
  const shouldPaginateFile = shouldUsePaginationForNode(mockFileNode);
  if (shouldPaginateFile) {
    console.error('❌ Ошибка: Файлы не должны использовать пагинацию');
    return false;
  }
  
  console.log('✅ shouldUsePaginationForNode работает корректно');
  return true;
};

/**
 * Тест функции создания URL с пагинацией
 */
export const testCreatePaginatedUrl = (): boolean => {
  console.log('🧪 Тестирование createPaginatedTreeUrl...');
  
  const endpoint = '/api/epc-tree';
  const baseParams = {
    cabinetId: 'cabinet-123',
    itemId: 'item-456',
    pos: 'node-789',
    isCabinet: false,
  };
  
  // Тест 1: URL без пагинации
  const urlWithoutPagination = createPaginatedTreeUrl(endpoint, baseParams);
  if (!urlWithoutPagination.includes('cabinetId=cabinet-123')) {
    console.error('❌ Ошибка: URL должен содержать базовые параметры');
    return false;
  }
  
  // Тест 2: URL с пагинацией
  const urlWithPagination = createPaginatedTreeUrl(endpoint, baseParams, 2, 10);
  if (!urlWithPagination.includes('page=2') || !urlWithPagination.includes('pageSize=10')) {
    console.error('❌ Ошибка: URL должен содержать параметры пагинации');
    return false;
  }
  
  console.log('✅ createPaginatedTreeUrl работает корректно');
  return true;
};

/**
 * Тест функции определения наличия дополнительных элементов
 */
export const testHasMoreItems = (): boolean => {
  console.log('🧪 Тестирование hasMoreItems...');
  
  const pageSize = 10;
  
  // Тест 1: Полная страница - есть еще элементы
  const hasMoreFull = hasMoreItems(10, pageSize);
  if (!hasMoreFull) {
    console.error('❌ Ошибка: При полной странице должны быть еще элементы');
    return false;
  }
  
  // Тест 2: Неполная страница - нет больше элементов
  const hasMorePartial = hasMoreItems(7, pageSize);
  if (hasMorePartial) {
    console.error('❌ Ошибка: При неполной странице не должно быть больше элементов');
    return false;
  }
  
  // Тест 3: Пустая страница - нет больше элементов
  const hasMoreEmpty = hasMoreItems(0, pageSize);
  if (hasMoreEmpty) {
    console.error('❌ Ошибка: При пустой странице не должно быть больше элементов');
    return false;
  }
  
  console.log('✅ hasMoreItems работает корректно');
  return true;
};

/**
 * Тест константы порога пагинации
 */
export const testPaginationThreshold = (): boolean => {
  console.log('🧪 Тестирование PAGINATION_THRESHOLD...');
  
  if (typeof PAGINATION_THRESHOLD !== 'number' || PAGINATION_THRESHOLD <= 0) {
    console.error('❌ Ошибка: PAGINATION_THRESHOLD должен быть положительным числом');
    return false;
  }
  
  console.log(`✅ PAGINATION_THRESHOLD = ${PAGINATION_THRESHOLD}`);
  return true;
};

/**
 * Запуск всех базовых тестов
 */
export const runBasicPaginationTests = (): boolean => {
  console.log('🚀 Запуск базовых тестов пагинации...\n');
  
  const tests = [
    testShouldUsePagination,
    testCreatePaginatedUrl,
    testHasMoreItems,
    testPaginationThreshold,
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    try {
      const result = test();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      console.error(`❌ Ошибка в тесте: ${error}`);
      allPassed = false;
    }
    console.log(''); // Пустая строка для разделения
  }
  
  if (allPassed) {
    console.log('🎉 Все базовые тесты пагинации прошли успешно!');
  } else {
    console.log('💥 Некоторые тесты не прошли. Проверьте реализацию.');
  }
  
  return allPassed;
};

// Экспорт для использования в других файлах
export default {
  runBasicPaginationTests,
  testShouldUsePagination,
  testCreatePaginatedUrl,
  testHasMoreItems,
  testPaginationThreshold,
};
