import { Button, Divider, Typography } from 'antd';
import classNames from 'classnames';
import { FC, ReactNode, useState } from 'react';

import { RenderPopupProps, renderPopup } from 'shared/model';
import styles from './styles.module.scss';

type ConfirmModalProps = {
  message: string | ReactNode;
  onClose: Callback;
  onConfirm: () => Promise<void> | void;
  buttonNames?: { cancel?: string; confirm?: string };
  className?: string;
  /** Закрытие в случае реджекта */
  closeOnReject?: boolean;
  isConfirmDanger?: boolean;
  onCloseAction?: Callback;
};

export type CreateConfirmModalOptions = (
  options: Omit<ConfirmModalProps, 'onClose'> & {
    title: string;
    closeOnEscape?: boolean;
    isCallback?: boolean;
    shouldCloseOnBackdropClick?: boolean;
  },
) => Callback | undefined;

const ConfirmModal: FC<ConfirmModalProps> = ({
  message,
  onConfirm,
  isConfirmDanger,
  buttonNames,
  className,
  onClose,
  closeOnReject,
  onCloseAction,
}) => {
  const [isPending, setIsPending] = useState(false);

  return (
    <>
      <Divider className={styles.divider} />
      <div className={classNames(styles.container, className)}>
        <div className={styles.content}>
          {typeof message === 'string' ? (
            <Typography.Text>{message}</Typography.Text>
          ) : (
            message
          )}

          <div className={styles.content__buttons}>
            <Button
              type="primary"
              id="modal-confirm-button"
              ghost
              danger={isConfirmDanger}
              loading={isPending}
              onClick={async () => {
                try {
                  setIsPending(true);
                  await onConfirm();
                  onClose();
                } catch (e) {
                  setIsPending(false);

                  if (closeOnReject) {
                    onClose();
                  }
                }
              }}
            >
              {buttonNames?.confirm || 'Подтвердить'}
            </Button>
            <Button
              className={classNames({ [styles.disabledButton]: isPending })}
              disabled={isPending}
              type="default"
              onClick={() => {
                if (onCloseAction) {
                  onCloseAction();
                }
                onClose();
              }}
              id="modal-close-button"
            >
              {buttonNames?.cancel || 'Отмена'}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export const createConfirmModal: CreateConfirmModalOptions = ({
  isCallback,
  shouldCloseOnBackdropClick = true,
  closeOnEscape = true,
  ...props
}) => {
  const options: RenderPopupProps = {
    popupUI: ({ onClose }) => <ConfirmModal onClose={onClose} {...props} />,
    className: styles.popup,
    title: props.title,
    titleClassName: styles.title,
    hideCloseButton: true,
    shouldCloseOnBackdropClick,
    closeOnEscape,
  };

  if (isCallback) {
    return () => renderPopup(options);
  }

  renderPopup(options);
  return undefined;
};
