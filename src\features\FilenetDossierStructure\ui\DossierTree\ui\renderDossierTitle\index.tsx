import {
  FileExcelOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { Typography } from 'antd';
import classNames from 'classnames';
import type { ReactNode } from 'react';

import { FilenetActions } from 'entities/FilenetActions';
import { ParsedDossierStructureTree } from 'entities/FilenetAttachmentsTree';
import styles from './styles.module.scss';

const iconsParams = {
  pdf: { icon: FilePdfOutlined, style: 'icon_pdf' },
  xls: { icon: FileExcelOutlined, style: 'icon_xls' },
  xlsx: { icon: FileExcelOutlined, style: 'icon_xls' },
  doc: { icon: FileWordOutlined, style: 'icon_doc' },
  docx: { icon: FileWordOutlined, style: 'icon_doc' },
  default: { icon: FileOutlined, style: '' },
};

export const renderDossierTitle = (
  node: ParsedDossierStructureTree,
): ReactNode => {
  const { isMain, iconType } = node;
  const FolderIcon = FolderOutlined;

  const iconData = iconsParams[iconType || 'default'] ?? iconsParams.default;
  const { icon: FileIcon, style: iconStyle } = iconData;

  return (
    <div
      className={classNames(styles.container, {
        [styles.fileContainer]: !node.isDirectory,
      })}
    >
      {node.isDirectory ? (
        <FolderIcon />
      ) : (
        <FileIcon
          className={classNames(styles.icon, {
            [styles[iconStyle]]: Boolean(iconStyle),
          })}
        />
      )}
      <Typography.Text
        className={classNames(
          node.isDirectory ? styles.title : styles.fileTitle,
        )}
      >
        {node.title}
      </Typography.Text>
      {isMain && <Typography.Text className={styles.main}>*</Typography.Text>}
      {!node.isDirectory && (
        <div className={styles.buttons}>
          <FilenetActions
            permissions={node.viewDownloadPermissions ?? []}
            cardId={node.id}
            attachment={(() => {
              const paths = String(node.title).split('/');

              return {
                id: String(node.attachmentId),
                filename: String(paths.at(-1)),
              };
            })()}
            hideNotEnoughRights
            size="small"
          />
        </div>
      )}
    </div>
  );
};
