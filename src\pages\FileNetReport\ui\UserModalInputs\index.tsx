import { DatePicker, Radio, Row, Space } from 'antd';
import moment from 'moment/moment';
import { FC } from 'react';
import {
  DATE_VARIANTS_LIST,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { InputRow } from 'shared/ui/InputRow';

import { selectors, reducers, enums } from '../../store';

import styles from './styles.module.scss';

export const UserModalInputs: FC = () => {
  const { isPending, periodFrom, periodTo, userStatus } = useAppSelector(
    selectors.userModalApiSelector,
  );
  const { setUserModalPeriod, setUserStatus } = useCreateSliceActions(
    reducers.slice.actions,
  );

  return (
    <Row>
      <InputRow title="За период">
        <DatePicker.RangePicker
          disabled={isPending}
          allowClear={false}
          className={styles.input}
          format={DATE_VARIANTS_LIST}
          onChange={(_, value) => setUserModalPeriod(value)}
          value={[
            moment(periodFrom, DEFAULT_DATE_VARIANT),
            moment(periodTo, DEFAULT_DATE_VARIANT),
          ]}
        />
      </InputRow>
      <InputRow title="Пользователи">
        <Radio.Group
          value={userStatus}
          buttonStyle="solid"
          onChange={(event) => setUserStatus(event.target.value)}
        >
          <Space direction="vertical">
            {Object.values(enums.UserStatus).map((value) => (
              <Radio value={value} key={value}>
                {value}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </InputRow>
    </Row>
  );
};
