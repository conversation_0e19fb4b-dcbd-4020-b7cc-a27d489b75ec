import { SearchOutlined } from '@ant-design/icons';
import { Input, InputRef } from 'antd';
import { FilterDropdownProps } from 'antd/lib/table/interface';
import { FC, Ref } from 'react';
import { ButtonsContainer } from 'shared/ui';
import { TableColumnData } from '../..';

import styles from './styles.module.scss';

export const FilterDropdown: FC<
  Pick<
    FilterDropdownProps,
    'selectedKeys' | 'setSelectedKeys' | 'confirm' | 'clearFilters'
  > & {
    searchInput: Ref<InputRef>;
    title?: TableColumnData['title'];
  }
> = ({
  selectedKeys,
  setSelectedKeys,
  clearFilters,
  searchInput,
  title,
  confirm,
}) => (
  <div className={styles.filtersDropdownContainer}>
    <Input
      maxLength={4000}
      ref={searchInput}
      placeholder={title ? `Поиск по: ${title}` : 'Поиск'}
      value={selectedKeys[0]}
      className={styles.filtersDropdownInput}
      onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
      onPressEnter={() => {
        confirm();
      }}
    />
    <ButtonsContainer
      className={styles.filtersDropdownButtons}
      buttons={[
        {
          type: 'primary',
          onClick: () => {
            confirm();
          },

          icon: <SearchOutlined />,
          size: 'small',
          key: 'search',
          title: 'Поиск',
        },
        {
          key: 'reset',
          title: 'Сбросить',
          onClick: () => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          },
          size: 'small',
        },
      ]}
    />
  </div>
);
